# 项目上下文信息

- MYJ在线项目：这是一个魔域私服游戏的自动化脚本项目，包含客户端自动登录、保持在线、验证码识别等功能。主要文件：client/base.py(核心类)、client/yzm.py(验证码处理)、server/server.py(服务端，目前为空)。技术栈：Python、requests、asyncio。
- 项目结构更新：实际核心文件在server/base.py(完整的MYJBase类)，server/js_parser.py(JavaScript解析器)，client目录只有配置文件。项目是完整的魔域私服自动化系统，包含登录、保持在线、JavaScript解析、邮件通知、多线程处理等高级功能。
- 已完成服务器-客户端架构开发：server/server.py(HTTP服务器，返回base.py内容)，client.py(HTTP客户端，请求并显示文件内容)，test_connection.py(连接测试脚本)，README.md(完整文档)。支持GET/POST请求，JSON响应，交互式菜单，自动保存文件等功能。
- 项目结构已简化：删除了server/server.py、client.py、test_connection.py、README.md等HTTP服务器相关文件。当前核心文件：server/base.py(完整的MYJBase类，包含JS解析器和魔域私服自动化功能)，client/账号配置/(配置文件.json、配置文件2.json)，client/辅助文本.txt。项目回归到纯粹的魔域私服自动化脚本，支持多账号配置和自动在线功能。
