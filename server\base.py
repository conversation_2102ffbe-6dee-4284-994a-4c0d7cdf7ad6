import os
import requests
import re
import asyncio
import time
import random
import json
import base64
from urllib.parse import quote  # 登录时,把中文账号转码使用
import httpx
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
import datetime
import ast
from typing import List, Dict, Any
from PyGameAuto32 import Thread
from threading import Lock      # 线程锁,用在命令

globals()['extra_codes'] = []
    
全局_验证码接口 = 'http://*************:5100/myj_yzm_yyy'

# JS处理开始

# 预编译正则表达式以提高性能
HTML_TAG_PATTERN = re.compile(r'<[^>]+>')
FUNCTION_PATTERN = re.compile(r'((?:p|parent)\.([a-zA-Z_][a-zA-Z0-9_]*)\s*\()')
INTEGER_PATTERN = re.compile(r'^-?\d+$')
FLOAT_PATTERN = re.compile(r'^-?\d+\.\d+$')
FUNCTION_CALL_PATTERN = re.compile(r'^[a-zA-Z_][a-zA-Z0-9_.]*\s*\(')

# 从Pet.js中提取的函数签名 - 用于精确匹配参数
FUNCTION_SIGNATURES = {
    'showI': ['arr', 'coinStr', 'aItemNum'],  # showI(arr,coinStr,aItemNum)
    'addNpcs': ['npcs'],  # addNpcs通常只有一个参数
    'addUser': ['userId', 'userInfo'],  # addUser通常有两个参数
    'addMY': ['message'],  # addMY通常只有一个消息参数
    'addCM': ['message'],  # addCM通常只有一个消息参数
    'setRoom': ['roomName', 'flag'],  # setRoom('小径03',true)
    '_roomDesc': ['roomName', 'desc', 'array'],  # _roomDesc('小径03','',[])
    '_getNpc': ['index', 'name', 'id', 'displayName', 'param4', 'param5', 'param6'],  # _getNpc的7个参数
}


def js_to_python(s:str):
    python_s = s.replace('true', 'True').replace('false', 'False').replace('null', 'None')
    return ast.literal_eval(python_s.strip())

def parse_js_code(code_snippet: str) -> List[Dict[str, Any]]:
    """
    JavaScript代码解析器 - 简单直接的方法

    Args:
        code_snippet: JavaScript代码片段

    Returns:
        List[Dict]: 按调用顺序的列表，每个元素是包含函数信息的字典
    """
    # 快速移除HTML标签
    clean_code = HTML_TAG_PATTERN.sub('', code_snippet)

    results = []
    pos = 0
    code_len = len(clean_code)

    # 直接查找所有函数调用
    while pos < code_len:
        # 查找 'p.' 或 'parent.'
        p_pos = clean_code.find('p.', pos)
        parent_pos = clean_code.find('parent.', pos)

        # 选择最近的位置
        if p_pos == -1 and parent_pos == -1:
            break
        elif p_pos == -1:
            next_pos = parent_pos
            prefix_len = 7  # len('parent.')
        elif parent_pos == -1:
            next_pos = p_pos
            prefix_len = 2  # len('p.')
        else:
            if p_pos < parent_pos:
                next_pos = p_pos
                prefix_len = 2
            else:
                next_pos = parent_pos
                prefix_len = 7

        # 解析函数调用
        func_info = simple_parse_function_call(clean_code, next_pos, prefix_len)
        if func_info:
            # 添加主函数
            results.append(func_info)

            # 不再处理子函数，保持简洁

            pos = func_info['end_pos']
        else:
            pos = next_pos + prefix_len

    # 直接返回列表
    return results

def simple_parse_function_call(code: str, start_pos: int, prefix_len: int) -> Dict[str, Any]:
    """
    简单的函数调用解析 - 回到基础版本，但加强showI处理
    """
    # 提取函数名
    func_start = start_pos + prefix_len
    paren_pos = code.find('(', func_start)
    if paren_pos == -1:
        return None

    function_name = code[func_start:paren_pos].strip()
    if not function_name:
        return None

    # 特殊处理showI函数 - 查找分号结束
    if function_name == 'showI':
        return parse_showi_with_semicolon(code, start_pos, prefix_len, paren_pos)

    # 其他函数使用标准解析
    return standard_parse_function_call(code, start_pos, prefix_len, paren_pos)

def parse_showi_with_semicolon(code: str, start_pos: int, prefix_len: int, paren_pos: int) -> Dict[str, Any]:
    """
    专门解析showI函数 - 查找分号结束位置
    """
    function_name = code[start_pos + prefix_len:paren_pos].strip()

    # 从左括号开始查找分号
    pos = paren_pos + 1
    paren_count = 1
    bracket_count = 0
    in_string = False
    string_char = None

    while pos < len(code):
        char = code[pos]

        if not in_string:
            if char in ['"', "'"]:
                in_string = True
                string_char = char
            elif char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
                if paren_count == 0:
                    # 找到函数结束的右括号
                    # 继续查找分号
                    end_pos = pos + 1
                    while end_pos < len(code) and code[end_pos] in [' ', '\t', '\n']:
                        end_pos += 1
                    if end_pos < len(code) and code[end_pos] == ';':
                        end_pos += 1

                    params_str = code[paren_pos + 1:pos]
                    raw_call = code[start_pos:end_pos]
                    parameters = fast_parse_parameters(params_str)

                    return {
                        'function_name': function_name,
                        'parameters': parameters,
                        'raw_call': raw_call,
                        'end_pos': end_pos
                    }
            elif char == '[':
                bracket_count += 1
            elif char == ']':
                bracket_count -= 1
        else:
            if char == string_char:
                # 检查转义
                escape_count = 0
                check_pos = pos - 1
                while check_pos >= 0 and code[check_pos] == '\\':
                    escape_count += 1
                    check_pos -= 1
                if escape_count % 2 == 0:
                    in_string = False
                    string_char = None

        pos += 1

    return None

def standard_parse_function_call(code: str, start_pos: int, prefix_len: int, paren_pos: int) -> Dict[str, Any]:
    """
    标准的函数调用解析
    """
    function_name = code[start_pos + prefix_len:paren_pos].strip()

    # 找到匹配的右括号
    paren_count = 1
    pos = paren_pos + 1
    in_string = False
    string_char = None

    while pos < len(code) and paren_count > 0:
        char = code[pos]

        if not in_string:
            if char in ['"', "'"]:
                in_string = True
                string_char = char
            elif char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
        else:
            if char == string_char:
                # 检查转义
                escape_count = 0
                check_pos = pos - 1
                while check_pos >= 0 and code[check_pos] == '\\':
                    escape_count += 1
                    check_pos -= 1
                if escape_count % 2 == 0:
                    in_string = False
                    string_char = None

        pos += 1

    if paren_count != 0:
        return None

    end_pos = pos
    params_str = code[paren_pos + 1:pos-1]
    raw_call = code[start_pos:end_pos]
    parameters = fast_parse_parameters(params_str)

    return {
        'function_name': function_name,
        'parameters': parameters,
        'raw_call': raw_call,
        'end_pos': end_pos
    }

def split_by_semicolon(code: str) -> List[str]:
    """
    按分号分割代码，但要考虑字符串和括号内的分号
    """
    statements = []
    current_statement = []
    in_string = False
    string_char = None
    paren_count = 0
    bracket_count = 0
    i = 0

    while i < len(code):
        char = code[i]

        if not in_string:
            if char in ['"', "'"]:
                in_string = True
                string_char = char
                current_statement.append(char)
            elif char == '(':
                paren_count += 1
                current_statement.append(char)
            elif char == ')':
                paren_count -= 1
                current_statement.append(char)
            elif char == '[':
                bracket_count += 1
                current_statement.append(char)
            elif char == ']':
                bracket_count -= 1
                current_statement.append(char)
            elif char == ';' and paren_count == 0 and bracket_count == 0:
                # 找到语句分隔符
                statement = ''.join(current_statement).strip()
                if statement:
                    statements.append(statement)
                current_statement = []
            else:
                current_statement.append(char)
        else:
            current_statement.append(char)
            if char == string_char:
                # 检查转义
                escape_count = 0
                j = i - 1
                while j >= 0 and code[j] == '\\':
                    escape_count += 1
                    j -= 1
                if escape_count % 2 == 0:
                    in_string = False
                    string_char = None

        i += 1

    # 处理最后一个语句
    if current_statement:
        statement = ''.join(current_statement).strip()
        if statement:
            statements.append(statement)

    return statements

def extract_function_calls_from_statement(statement: str) -> List[Dict[str, Any]]:
    """
    从单个语句中提取所有函数调用 - 改进版，处理多个相同函数
    """
    functions = []

    # 先检查是否包含多个函数调用（通过查找多个p.或parent.）
    func_positions = []
    pos = 0
    while pos < len(statement):
        p_pos = statement.find('p.', pos)
        parent_pos = statement.find('parent.', pos)

        if p_pos == -1 and parent_pos == -1:
            break
        elif p_pos == -1:
            func_positions.append((parent_pos, 7))
            pos = parent_pos + 7
        elif parent_pos == -1:
            func_positions.append((p_pos, 2))
            pos = p_pos + 2
        else:
            if p_pos < parent_pos:
                func_positions.append((p_pos, 2))
                pos = p_pos + 2
            else:
                func_positions.append((parent_pos, 7))
                pos = parent_pos + 7

    # 逐个解析每个函数调用
    for i, (start_pos, prefix_len) in enumerate(func_positions):
        # 确定这个函数调用的结束边界
        if i + 1 < len(func_positions):
            # 不是最后一个函数，结束位置不能超过下一个函数的开始位置
            max_end_pos = func_positions[i + 1][0]
            limited_statement = statement[start_pos:max_end_pos]
        else:
            # 最后一个函数，可以到语句结尾
            limited_statement = statement[start_pos:]

        # 解析这个函数调用
        func_info = parse_function_call_with_limit(limited_statement, 0, prefix_len)
        if func_info:
            # 调整位置信息
            func_info['end_pos'] = start_pos + func_info['end_pos']
            functions.append(func_info)

            # 特殊处理addNpcs函数
            if func_info['function_name'] == 'addNpcs':
                npc_params = extract_addnpcs_parameters(func_info)
                func_info['npc_list'] = npc_params

            # 解析参数中的子函数
            sub_functions = extract_sub_functions(func_info['parameters'])
            functions.extend(sub_functions)

    return functions

def parse_function_call_with_limit(statement: str, start_pos: int, prefix_len: int) -> Dict[str, Any]:
    """
    解析函数调用，但限制在指定的语句范围内
    """
    # 提取函数名
    func_start = start_pos + prefix_len
    paren_pos = statement.find('(', func_start)
    if paren_pos == -1:
        return None

    function_name = statement[func_start:paren_pos].strip()
    if not function_name:
        return None

    # 找到匹配的右括号
    paren_count = 1
    pos = paren_pos + 1
    params_start = pos
    in_string = False
    string_char = None

    while pos < len(statement) and paren_count > 0:
        char = statement[pos]

        if not in_string:
            if char in ['"', "'"]:
                in_string = True
                string_char = char
            elif char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
        else:
            if char == string_char:
                # 检查转义
                escape_count = 0
                check_pos = pos - 1
                while check_pos >= 0 and statement[check_pos] == '\\':
                    escape_count += 1
                    check_pos -= 1
                if escape_count % 2 == 0:
                    in_string = False
                    string_char = None

        pos += 1

    if paren_count != 0:
        return None

    end_pos = pos
    params_str = statement[params_start:pos-1]
    raw_call = statement[start_pos:end_pos]

    # 解析参数
    parameters = fast_parse_parameters(params_str)

    return {
        'function_name': function_name,
        'parameters': parameters,
        'raw_call': raw_call,
        'end_pos': end_pos
    }

def parse_function_call_simple(statement: str, start_pos: int, prefix_len: int) -> Dict[str, Any]:
    """
    简单的函数调用解析 - 基于已知函数签名
    """
    # 提取函数名
    func_start = start_pos + prefix_len
    paren_pos = statement.find('(', func_start)
    if paren_pos == -1:
        return None

    function_name = statement[func_start:paren_pos].strip()
    if not function_name:
        return None

    # 使用函数签名来确定参数数量
    expected_params = FUNCTION_SIGNATURES.get(function_name, None)

    # 找到匹配的右括号
    paren_count = 1
    pos = paren_pos + 1
    params_start = pos

    while pos < len(statement) and paren_count > 0:
        char = statement[pos]
        if char == '(':
            paren_count += 1
        elif char == ')':
            paren_count -= 1
        elif char in ['"', "'"]:
            # 跳过字符串内容
            quote_char = char
            pos += 1
            while pos < len(statement) and statement[pos] != quote_char:
                if statement[pos] == '\\':
                    pos += 1
                pos += 1
        pos += 1

    if paren_count != 0:
        return None

    end_pos = pos
    params_str = statement[params_start:pos-1]
    raw_call = statement[start_pos:end_pos]

    # 验证函数调用的合理性
    if expected_params and function_name == 'showI':
        # showI函数特殊验证：参数中不应该包含其他函数调用
        if 'p.' in params_str.replace('"', '').replace("'", ''):
            # 可能包含了错误的内容，尝试重新定位
            corrected_end = find_showi_end_by_signature(statement, start_pos, paren_pos)
            if corrected_end and corrected_end < end_pos:
                end_pos = corrected_end
                params_str = statement[params_start:end_pos-1]
                raw_call = statement[start_pos:end_pos]

    # 解析参数
    parameters = fast_parse_parameters(params_str)

    return {
        'function_name': function_name,
        'parameters': parameters,
        'raw_call': raw_call,
        'end_pos': end_pos
    }

def find_showi_end_by_signature(statement: str, _start_pos: int, paren_pos: int) -> int:
    """
    基于showI函数签名找到正确的结束位置
    showI(arr,coinStr,aItemNum) - 应该有3个参数
    """
    pos = paren_pos + 1
    param_count = 0
    paren_count = 1
    bracket_count = 0
    in_string = False
    string_char = None

    while pos < len(statement) and paren_count > 0:
        char = statement[pos]

        if not in_string:
            if char in ['"', "'"]:
                in_string = True
                string_char = char
            elif char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
                if paren_count == 0:
                    # 找到了函数结束
                    return pos + 1
            elif char == '[':
                bracket_count += 1
            elif char == ']':
                bracket_count -= 1
            elif char == ',' and paren_count == 1 and bracket_count == 0:
                # 参数分隔符
                param_count += 1
                # showI应该有3个参数，如果已经有2个逗号，下一个右括号就是结束
                if param_count >= 2:
                    # 寻找下一个右括号
                    temp_pos = pos + 1
                    temp_paren = 1
                    temp_bracket = bracket_count
                    temp_in_string = False
                    temp_string_char = None

                    while temp_pos < len(statement) and temp_paren > 0:
                        temp_char = statement[temp_pos]
                        if not temp_in_string:
                            if temp_char in ['"', "'"]:
                                temp_in_string = True
                                temp_string_char = temp_char
                            elif temp_char == '(':
                                temp_paren += 1
                            elif temp_char == ')':
                                temp_paren -= 1
                                if temp_paren == 0 and temp_bracket == 0:
                                    return temp_pos + 1
                            elif temp_char == '[':
                                temp_bracket += 1
                            elif temp_char == ']':
                                temp_bracket -= 1
                        else:
                            if temp_char == temp_string_char:
                                escape_count = 0
                                check_pos = temp_pos - 1
                                while check_pos >= 0 and statement[check_pos] == '\\':
                                    escape_count += 1
                                    check_pos -= 1
                                if escape_count % 2 == 0:
                                    temp_in_string = False
                                    temp_string_char = None
                        temp_pos += 1
        else:
            if char == string_char:
                escape_count = 0
                check_pos = pos - 1
                while check_pos >= 0 and statement[check_pos] == '\\':
                    escape_count += 1
                    check_pos -= 1
                if escape_count % 2 == 0:
                    in_string = False
                    string_char = None

        pos += 1

    return None

def extract_sub_functions(parameters: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    从参数中提取子函数调用
    """
    sub_functions = []

    for param in parameters:
        if param['type'] == 'function_call':
            # 解析子函数
            sub_func_info = parse_single_function_call(param['raw'])
            if sub_func_info:
                sub_functions.append(sub_func_info)

                # 递归解析子函数的参数中是否还有函数
                nested_sub_functions = extract_sub_functions(sub_func_info['parameters'])
                sub_functions.extend(nested_sub_functions)
        elif param['type'] == 'identifier' and '+' in param['raw']:
            # 处理字符串拼接中的函数调用，如 p._getNpc(...)+p._getNpc(...)+''
            sub_funcs_in_concat = extract_functions_from_concatenation(param['raw'])
            sub_functions.extend(sub_funcs_in_concat)

    return sub_functions

def extract_functions_from_concatenation(concat_str: str) -> List[Dict[str, Any]]:
    """
    从字符串拼接表达式中提取函数调用
    专门处理 p._getNpc(...)+p._getNpc(...)+'' 这种情况
    """
    functions = []
    pos = 0

    while pos < len(concat_str):
        # 查找 p. 开头的函数调用
        p_pos = concat_str.find('p.', pos)
        if p_pos == -1:
            break

        # 找到函数名结束位置（左括号）
        paren_pos = concat_str.find('(', p_pos)
        if paren_pos == -1:
            pos = p_pos + 2
            continue

        # 找到匹配的右括号
        paren_count = 1
        end_pos = paren_pos + 1

        while end_pos < len(concat_str) and paren_count > 0:
            char = concat_str[end_pos]
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
            elif char in ['"', "'"]:
                # 跳过字符串内容
                quote_char = char
                end_pos += 1
                while end_pos < len(concat_str) and concat_str[end_pos] != quote_char:
                    if concat_str[end_pos] == '\\':
                        end_pos += 1
                    end_pos += 1
            end_pos += 1

        if paren_count == 0:
            # 提取完整的函数调用
            func_call = concat_str[p_pos:end_pos]
            func_info = parse_single_function_call(func_call)
            if func_info:
                functions.append(func_info)

        pos = end_pos

    return functions

def extract_addnpcs_parameters(func_info: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    专门提取addNpcs函数中所有_getNpc调用的参数
    返回格式: [{'npc_id': 0, 'name': '绿毛虫', 'id': 4369722721921, ...}, ...]
    """
    npc_list = []

    # 从函数的原始调用中提取所有_getNpc
    raw_call = func_info['raw_call']

    # 找到参数部分
    paren_start = raw_call.find('(')
    paren_end = raw_call.rfind(')')
    if paren_start == -1 or paren_end == -1:
        return npc_list

    params_content = raw_call[paren_start + 1:paren_end]

    # 查找所有_getNpc调用
    pos = 0
    while pos < len(params_content):
        getnpc_pos = params_content.find('p._getNpc(', pos)
        if getnpc_pos == -1:
            break

        # 找到这个_getNpc调用的结束位置
        paren_count = 1
        start_pos = getnpc_pos + 10  # len('p._getNpc(')
        end_pos = start_pos

        while end_pos < len(params_content) and paren_count > 0:
            char = params_content[end_pos]
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
            elif char in ['"', "'"]:
                # 跳过字符串
                quote_char = char
                end_pos += 1
                while end_pos < len(params_content) and params_content[end_pos] != quote_char:
                    if params_content[end_pos] == '\\':
                        end_pos += 1
                    end_pos += 1
            end_pos += 1

        if paren_count == 0:
            # 提取_getNpc的参数
            npc_params_str = params_content[start_pos:end_pos-1]
            npc_params = fast_parse_parameters(npc_params_str)

            # 转换为更友好的格式
            if len(npc_params) >= 7:  # _getNpc通常有7个参数
                npc_dict = {
                    'index': npc_params[0]['value'] if npc_params[0]['type'] in ['integer', 'float'] else npc_params[0]['raw'],
                    'name': npc_params[1]['value'] if npc_params[1]['type'] == 'string' else npc_params[1]['raw'],
                    'id': npc_params[2]['value'] if npc_params[2]['type'] in ['integer', 'float'] else npc_params[2]['raw'],
                    'display_name': npc_params[3]['value'] if npc_params[3]['type'] == 'string' else npc_params[3]['raw'],
                    'param4': npc_params[4]['value'] if len(npc_params) > 4 else None,
                    'param5': npc_params[5]['value'] if len(npc_params) > 5 else None,
                    'param6': npc_params[6]['value'] if len(npc_params) > 6 else None,
                    'raw_params': npc_params  # 保留原始参数信息
                }
                npc_list.append(npc_dict)

        pos = end_pos

    return npc_list

def parse_single_function_call(func_call: str) -> Dict[str, Any]:
    """
    解析单个函数调用字符串
    """
    # 查找函数名
    if func_call.startswith('p.'):
        prefix_len = 2
    elif func_call.startswith('parent.'):
        prefix_len = 7
    else:
        return None

    paren_pos = func_call.find('(')
    if paren_pos == -1:
        return None

    function_name = func_call[prefix_len:paren_pos].strip()

    # 找到匹配的右括号
    paren_count = 1
    pos = paren_pos + 1
    params_start = pos

    while pos < len(func_call) and paren_count > 0:
        char = func_call[pos]
        if char == '(':
            paren_count += 1
        elif char == ')':
            paren_count -= 1
        elif char in ['"', "'"]:
            # 跳过字符串内容
            quote_char = char
            pos += 1
            while pos < len(func_call) and func_call[pos] != quote_char:
                if func_call[pos] == '\\':
                    pos += 1
                pos += 1
        pos += 1

    if paren_count != 0:
        return None

    params_str = func_call[params_start:pos-1]
    parameters = fast_parse_parameters(params_str)

    return {
        'function_name': function_name,
        'parameters': parameters,
        'raw_call': func_call,
        'is_sub_function': True  # 标记为子函数
    }

def fast_parse_function_call(code: str, start_pos: int, prefix_len: int) -> Dict[str, Any]:
    """
    快速解析单个函数调用 - 精确的括号匹配算法
    """
    # 提取函数名
    func_start = start_pos + prefix_len
    paren_pos = code.find('(', func_start)
    if paren_pos == -1:
        return None

    function_name = code[func_start:paren_pos].strip()
    if not function_name or not function_name.replace('_', '').replace('$', '').isalnum():
        return None

    # 精确的括号匹配算法 - 严格按照括号层级
    paren_count = 1
    bracket_count = 0
    pos = paren_pos + 1
    params_start = pos
    in_string = False
    string_char = None

    while pos < len(code) and paren_count > 0:
        char = code[pos]

        if not in_string:
            if char in ['"', "'"]:
                in_string = True
                string_char = char
            elif char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
                # 当paren_count为0时，找到了函数的结束位置
                if paren_count == 0:
                    break
            elif char == '[':
                bracket_count += 1
            elif char == ']':
                bracket_count -= 1
        else:
            # 在字符串内部
            if char == string_char:
                # 检查是否是转义的引号
                escape_count = 0
                check_pos = pos - 1
                while check_pos >= 0 and code[check_pos] == '\\':
                    escape_count += 1
                    check_pos -= 1
                if escape_count % 2 == 0:  # 偶数个反斜杠，引号未被转义
                    in_string = False
                    string_char = None

        pos += 1

    # 验证解析结果
    if paren_count != 0:
        return None

    # 最终位置应该在右括号之后
    end_pos = pos + 1
    params_str = code[params_start:pos]
    raw_call = code[start_pos:end_pos]

    # 额外验证：检查raw_call是否包含了不应该存在的函数调用
    if function_name == 'showI':
        # showI函数内部不应该包含p.或parent.函数调用
        inner_content = params_str
        # 移除字符串内容后检查
        cleaned_inner = remove_string_content(inner_content)
        if 'p.' in cleaned_inner or 'parent.' in cleaned_inner:
            # 可能解析错误，尝试重新定位结束位置
            corrected_end = find_correct_end_for_showi(code, start_pos, paren_pos)
            if corrected_end and corrected_end < end_pos:
                end_pos = corrected_end
                params_str = code[params_start:end_pos-1]
                raw_call = code[start_pos:end_pos]

    # 快速解析参数
    parameters = fast_parse_parameters(params_str)

    return {
        'function_name': function_name,
        'parameters': parameters,
        'raw_call': raw_call,
        'end_pos': end_pos
    }

def remove_string_content(text: str) -> str:
    """移除字符串内容，用于检查是否包含函数调用"""
    result = []
    in_string = False
    string_char = None
    i = 0

    while i < len(text):
        char = text[i]
        if not in_string:
            if char in ['"', "'"]:
                in_string = True
                string_char = char
                result.append(' ')  # 用空格替代字符串
            else:
                result.append(char)
        else:
            if char == string_char:
                # 检查转义
                escape_count = 0
                j = i - 1
                while j >= 0 and text[j] == '\\':
                    escape_count += 1
                    j -= 1
                if escape_count % 2 == 0:
                    in_string = False
                    string_char = None
        i += 1

    return ''.join(result)

def find_correct_end_for_showi(code: str, _start_pos: int, paren_pos: int) -> int:
    """为showI函数找到正确的结束位置"""
    # showI通常的格式: p.showI([...]);
    # 寻找 ]); 模式
    pos = paren_pos + 1
    bracket_count = 0
    in_string = False
    string_char = None

    while pos < len(code):
        char = code[pos]

        if not in_string:
            if char in ['"', "'"]:
                in_string = True
                string_char = char
            elif char == '[':
                bracket_count += 1
            elif char == ']':
                bracket_count -= 1
                if bracket_count == 0:
                    # 找到了数组的结束，检查后面是否是 );
                    next_pos = pos + 1
                    while next_pos < len(code) and code[next_pos] in [' ', '\t', '\n']:
                        next_pos += 1
                    if next_pos < len(code) and code[next_pos] == ')':
                        return next_pos + 1
            elif char == 'p' and bracket_count == 0:
                # 在数组外遇到p，可能是新的函数调用
                if pos + 1 < len(code) and code[pos + 1] == '.':
                    # 这里应该是showI的结束位置
                    # 向前查找最近的 );
                    back_pos = pos - 1
                    while back_pos > paren_pos:
                        if code[back_pos:back_pos+2] == ');':
                            return back_pos + 2
                        back_pos -= 1
        else:
            if char == string_char:
                escape_count = 0
                check_pos = pos - 1
                while check_pos >= 0 and code[check_pos] == '\\':
                    escape_count += 1
                    check_pos -= 1
                if escape_count % 2 == 0:
                    in_string = False
                    string_char = None

        pos += 1

    return None

def fast_parse_parameters(params_str: str) -> List[Dict[str, Any]]:
    """
    高性能参数解析
    """
    if not params_str.strip():
        return []

    parameters = []
    current_param = []
    paren_count = 0
    bracket_count = 0
    in_string = False
    string_char = None
    i = 0

    while i < len(params_str):
        char = params_str[i]

        if not in_string:
            if char in ['"', "'"]:
                in_string = True
                string_char = char
                current_param.append(char)
            elif char == '(':
                paren_count += 1
                current_param.append(char)
            elif char == ')':
                paren_count -= 1
                current_param.append(char)
            elif char == '[':
                bracket_count += 1
                current_param.append(char)
            elif char == ']':
                bracket_count -= 1
                current_param.append(char)
            elif char == ',' and paren_count == 0 and bracket_count == 0:
                # 参数分隔符
                param_str = ''.join(current_param).strip()
                if param_str:
                    parameters.append(fast_analyze_parameter(param_str))
                current_param = []
            else:
                current_param.append(char)
        else:
            current_param.append(char)
            if char == string_char:
                # 检查是否是转义的引号
                escape_count = 0
                j = i - 1
                while j >= 0 and params_str[j] == '\\':
                    escape_count += 1
                    j -= 1
                if escape_count % 2 == 0:  # 偶数个反斜杠，引号未被转义
                    in_string = False
                    string_char = None

        i += 1

    # 处理最后一个参数
    if current_param:
        param_str = ''.join(current_param).strip()
        if param_str:
            parameters.append(fast_analyze_parameter(param_str))

    return parameters

def fast_analyze_parameter(param: str) -> Dict[str, Any]:
    """
    高性能参数类型分析
    """
    if not param:
        return {'type': 'empty', 'value': '', 'raw': param}

    first_char = param[0]
    last_char = param[-1]

    # 字符串类型 - 最常见，优先检查
    if (first_char == '"' and last_char == '"') or \
       (first_char == "'" and last_char == "'"):
        return {
            'type': 'string',
            'value': param[1:-1],
            'raw': param
        }

    # 数组类型
    if first_char == '[' and last_char == ']':
        return {
            'type': 'array',
            'value': param,
            'raw': param
        }

    # 数字类型 - 使用字符检查而不是正则
    if first_char.isdigit() or (first_char == '-' and len(param) > 1 and param[1].isdigit()):
        if '.' in param:
            try:
                return {
                    'type': 'float',
                    'value': float(param),
                    'raw': param
                }
            except ValueError:
                pass
        else:
            try:
                return {
                    'type': 'integer',
                    'value': int(param),
                    'raw': param
                }
            except ValueError:
                pass

    # 布尔和null类型 - 使用字典查找
    lower_param = param.lower()
    if lower_param == 'true':
        return {'type': 'boolean', 'value': True, 'raw': param}
    elif lower_param == 'false':
        return {'type': 'boolean', 'value': False, 'raw': param}
    elif lower_param in ['null', 'undefined']:
        return {'type': 'null', 'value': None, 'raw': param}

    # 函数调用 - 简单检查
    if '(' in param and param.find('(') < param.rfind(')'):
        return {
            'type': 'function_call',
            'value': param,
            'raw': param
        }

    # 默认为标识符
    return {
        'type': 'identifier',
        'value': param,
        'raw': param
    }

def ret_packages(func_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    处理showI函数的背包数据，解析物品信息

    Args:
        func_data: showI函数的解析数据，包含function_name和parameters

    Returns:
        List[Dict]: 物品信息列表，每个物品包含名称、数量、单位等信息
    """
    if func_data.get('function_name') != 'showI':
        return []

    parameters = func_data.get('parameters', [])
    if not parameters or len(parameters) == 0:
        return []

    # showI的第一个参数应该是物品数组
    first_param = parameters[0]
    if first_param.get('type') != 'array':
        return []

    # 解析数组字符串
    array_str = first_param.get('value', '')
    if not array_str.startswith('[') or not array_str.endswith(']'):
        return []

    # 解析物品数据
    items = parse_item_array(array_str)

    # 转换为结构化数据
    packages = []
    for item in items:
        if len(item) >= 18:  # 确保有足够的字段
            package_info = {
                'name': item[0] if len(item) > 0 else '',           # 物品名称
                'quantity': item[1] if len(item) > 1 else 0,        # 数量
                'unit': item[2] if len(item) > 2 else '',           # 单位
                'index': item[3] if len(item) > 3 else 0,           # 索引
                'icon': item[4] if len(item) > 4 else '',           # 图标路径
                'sellable': item[5] if len(item) > 5 else 'false',  # 是否可卖
                'usable': item[6] if len(item) > 6 else False,      # 是否可用
                'tradeable': item[7] if len(item) > 7 else False,   # 是否可交易
                'big_icon': item[8] if len(item) > 8 else '',       # 大图标
                'item_id': item[9] if len(item) > 9 else '',        # 物品ID
                'stackable': item[10] if len(item) > 10 else False, # 是否可堆叠
                'color': item[11] if len(item) > 11 else '000000',  # 颜色
                'param12': item[12] if len(item) > 12 else False,   # 参数12
                'param13': item[13] if len(item) > 13 else False,   # 参数13
                'param14': item[14] if len(item) > 14 else False,   # 参数14
                'display_name': item[15] if len(item) > 15 else '', # 显示名称
                'param16': item[16] if len(item) > 16 else False,   # 参数16
                'param17': item[17] if len(item) > 17 else False,   # 参数17
            }
            packages.append(package_info)

    return packages

def parse_item_array(array_str: str) -> List[List]:
    """
    解析物品数组字符串

    Args:
        array_str: 数组字符串，如 "[['物品1',1,'个',...],['物品2',2,'个',...]]"

    Returns:
        List[List]: 解析后的物品数据列表
    """
    items = []

    # 移除外层方括号
    content = array_str[1:-1].strip()

    # 解析每个物品数组
    pos = 0
    while pos < len(content):
        # 跳过空白字符
        while pos < len(content) and content[pos] in [' ', '\t', '\n', ',']:
            pos += 1

        if pos >= len(content):
            break

        # 查找物品数组开始
        if content[pos] != '[':
            pos += 1
            continue

        # 解析单个物品数组
        item_start = pos
        bracket_count = 1
        pos += 1
        in_string = False
        string_char = None

        while pos < len(content) and bracket_count > 0:
            char = content[pos]

            if not in_string:
                if char in ['"', "'"]:
                    in_string = True
                    string_char = char
                elif char == '[':
                    bracket_count += 1
                elif char == ']':
                    bracket_count -= 1
            else:
                if char == string_char:
                    # 检查转义
                    escape_count = 0
                    check_pos = pos - 1
                    while check_pos >= 0 and content[check_pos] == '\\':
                        escape_count += 1
                        check_pos -= 1
                    if escape_count % 2 == 0:
                        in_string = False
                        string_char = None

            pos += 1

        if bracket_count == 0:
            # 提取物品数组字符串
            item_str = content[item_start:pos]
            item_data = parse_single_item(item_str)
            if item_data:
                items.append(item_data)

    return items

def parse_single_item(item_str: str) -> List:
    """
    解析单个物品数组

    Args:
        item_str: 单个物品数组字符串，如 "['物品名',1,'个',...]"

    Returns:
        List: 物品数据列表
    """
    # 移除方括号
    content = item_str[1:-1].strip()

    # 解析参数
    params = []
    pos = 0
    current_param = []
    in_string = False
    string_char = None

    while pos < len(content):
        char = content[pos]

        if not in_string:
            if char in ['"', "'"]:
                in_string = True
                string_char = char
                current_param.append(char)
            elif char == ',':
                # 参数分隔符
                param_str = ''.join(current_param).strip()
                if param_str:
                    params.append(parse_item_param(param_str))
                current_param = []
            else:
                current_param.append(char)
        else:
            current_param.append(char)
            if char == string_char:
                # 检查转义
                escape_count = 0
                check_pos = pos - 1
                while check_pos >= 0 and content[check_pos] == '\\':
                    escape_count += 1
                    check_pos -= 1
                if escape_count % 2 == 0:
                    in_string = False
                    string_char = None

        pos += 1

    # 处理最后一个参数
    if current_param:
        param_str = ''.join(current_param).strip()
        if param_str:
            params.append(parse_item_param(param_str))

    return params

def parse_item_param(param_str: str) -> Any:
    """
    解析单个物品参数

    Args:
        param_str: 参数字符串

    Returns:
        Any: 解析后的参数值
    """
    param_str = param_str.strip()

    # 字符串类型
    if (param_str.startswith('"') and param_str.endswith('"')) or \
       (param_str.startswith("'") and param_str.endswith("'")):
        return param_str[1:-1]

    # 布尔类型
    if param_str.lower() == 'true':
        return True
    elif param_str.lower() == 'false':
        return False

    # 数字类型
    try:
        if '.' in param_str:
            return float(param_str)
        else:
            return int(param_str)
    except ValueError:
        pass

    # 其他类型返回原字符串
    return param_str

def func_test_js_parser():
    import time
    import json

    # 性能测试代码
    test_code = """
    信息收发器...                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     
    OK!
    <script>var p=parent;p.initWorker();</script>
    <script>p.cls();p.cps('zone');p.setRoom('道具店',true);p._roomDesc('道具店','',[]);p.addNpcs(p._getNpc(0,'道具店老板',16262388592480,'道具店老板',1,true,false)+p._getNpc(1,'李金斗',16262408843209,'<font color=green>当铺老板</font> 李金斗',0,true,false)+p._getNpc(2,'王小二',16262429093938,'<font color=blue>道具店跑堂</font> 王小二',1,true,false)+'');</script><script>p.showI([['传送石',1,'块',0,'img/itemlogo/other/ChuanSongShi.gif','false',true,true,'img/itemlogo/other/ChuanSongShi.gif','fitems.pet.other.ChuanSongShi',false,'000000',false,false,false,'传送石',false,false],['月光的祝福',4,'支',1,'img/itemlogo/yao/YueGuangZhuFu.gif','false',true,true,'img/itemlogo/yao/YueGuangZhuFu.gif','fitems.pet.huifu.fightstatus.YueGuang_1_G',false,'008800',false,false,true,'月光的祝福',false,false],['每曰二魔纹包(6)',1,'只',2,'img/itemlogo/item/budai.gif','false',true,false,'img/itemlogo/item/budai.gif','fitems.pet.other.Bag12',false,'000000',false,false,true,'每曰二魔纹包(6)',false,false],['竹蜻蜓',81,'支',3,'img/itemlogo/item/zhuqingting.gif','false',true,true,'img/itemlogo/item/zhuqingting.gif','fitems.pet.huifu.ZhuQingTing',false,'000000',false,false,true,'竹蜻蜓',false,false],['神符石',445,'枚',4,'img/shenfu/ShenFuShi_Lv.gif','false',true,false,'img/shenfu/ShenFuShi_Lv.gif','fitems.shenfu.ShenFuShi_Lv',false,'008800',false,false,true,'神符石',false,false],['小号材料丝绸包(5)',1,'只',5,'img/itemlogo/item/budai.gif','false',true,false,'img/itemlogo/item/budai.gif','fitems.pet.other.Bag8',false,'000000',false,false,true,'小号材料丝绸包(5)',false,false],['白色羽毛',99,'支',6,'img/itemlogo/item/baiyumao.gif','false',true,true,'img/itemlogo/item/baiyumao.gif','fitems.pet.huifu.BaiYuMao',false,'000000',false,false,true,'白色羽毛',false,false],['每曰魔纹包(8)',1,'只',7,'img/itemlogo/item/budai.gif','false',true,false,'img/itemlogo/item/budai.gif','fitems.pet.other.Bag12',false,'000000',false,false,true,'每曰魔纹包(8)',false,false],['秘银宝箱',203,'只',8,'img/itemlogo/item/Boxb.gif','false',true,false,'img/itemlogo/item/Boxb.gif','fitems.pet.other.BoxMiYin',false,'008800',false,false,true,'秘银宝箱',false,false],['苹果',727,'个',9,'img/itemlogo/stuff/apple.gif','false',true,true,'img/itemlogo/stuff/apple.gif','fitems.pet.complex.PingGuo',false,'000000',false,false,true,'苹果',false,false],['守护灵经验书I',302,'个',10,'img/itemlogo/shouhuling/SHLJingYanShu.gif','false',false,false,'img/itemlogo/shouhuling/SHLJingYanShu.gif','fitems.shouhuling.exp.SHLJingYanShu1',false,'000000',false,false,true,'守护灵经验书I',false,false],['昆虫外壳',427,'只',11,'img/itemlogo/stuff/kunchongwaike.gif','false',false,false,'img/itemlogo/stuff/kunchongwaike.gif','fitems.pet.complex.KunChongWaiKe',false,'000000',false,false,true,'昆虫外壳',false,false],['小型补血剂',74,'支',12,'img/bar/prop/HongPing.gif','false',true,true,'img/bar/prop/HongPing.gif','fitems.pet.huifu.HongPing1',false,'000000',false,false,true,'小型补血剂',false,false],['经验卷轴I',55,'卷',13,'img/itemlogo/item/ExpRollLv1.gif','false',true,false,'img/itemlogo/item/ExpRollLv1.gif','fitems.experience.ExpRollLv1',false,'000000',false,false,true,'经验卷轴I',false,false],['蜜蜂卡片',34,'张',14,'img/itemlogo/card.gif','false',true,false,'img/itemlogo/card.gif','fitems.card.xie.MiFeng',false,'000000',false,false,true,'蜜蜂卡片',false,false],['蚕蛹卡片',23,'张',15,'img/itemlogo/card.gif','false',true,false,'img/itemlogo/card.gif','fitems.card.dun.CanYong',false,'000000',false,false,true,'蚕蛹卡片',false,false],['泡泡图鉴',1,'张',16,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['钢筋铁骨1级技能果实',13,'个',17,'img/itemlogo/shouhuling/GuoShi.gif','false',false,false,'img/itemlogo/shouhuling/GuoShi.gif','fitems.shouhuling.other.GangJinTieGuGuoShilv1',false,'008800',false,false,true,'钢筋铁骨1级技能果实',false,false],['泡泡图鉴',1,'张',18,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['绿毛虫图鉴',1,'张',19,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['许愿绳',32,'根',20,'img/itemlogo/XuYuanSheng.gif','false',false,false,'img/itemlogo/XuYuanSheng.gif','fitems.xuyuanshu.XuYuanSheng',false,'737373',false,true,true,'许愿绳',false,false],['法宝精化石',16,'枚',21,'img/itemlogo/fabao/FaBaoJingHuaShi.gif','false',true,false,'img/itemlogo/fabao/FaBaoJingHuaShi.gif','fitems.fabao.FaBaoJingHuaShi_G',false,'008800',false,false,true,'法宝精化石',false,false],['初级升级石',25,'个',22,'img/itemlogo/UpgradeStoneBlue.gif','false',true,false,'img/itemlogo/UpgradeStoneBlue.gif','fitems.card.weapon.UpgradeStoneLv30Blue',false,'0070DD',false,false,true,'初级升级石',false,false],['牛鼻环',3,'只',23,'img/itemlogo/stuff/niubihuan.gif','false',false,false,'img/itemlogo/stuff/niubihuan.gif','fitems.pet.complex.NiuBiHuan',false,'000000',false,false,true,'牛鼻环',false,false],['短矛',1,'把',24,'img/arm/weapon/qiang/DuanMao.gif','',false,false,'img/arm/weapon/qiang/DuanMao.gif','fitems.arm.weapon.qiang.lv020.common.DuanMao',false,'737373',false,false,true,'短矛',false,false],['神通镜',1,'面',25,'img/itemlogo/task/ShenTongJing.gif','false',true,false,'img/itemlogo/task/ShenTongJing.gif','fitems.task.xueyuancheng.ShenTongJing',false,'000000',false,false,true,'神通镜',false,false],['银雪平原BOSS奇美拉的头',1,'颗',26,'img/itemlogo/task/QiMeiLaBossHead.gif','false',false,false,'img/itemlogo/task/QiMeiLaBossHead.gif','fitems.task.yinxuepingyuan.QiMeiLaBossHead',false,'000000',false,false,true,'银雪平原BOSS奇美拉的头',false,false],['世界碎片',1,'片',27,'img/itemlogo/other/YinYue_4.gif','false',false,false,'img/itemlogo/other/YinYue_4.gif','fitems.task.mopcity.ShiJieSuiPian',false,'000000',false,false,true,'世界碎片',false,false],['战神令',19,'只',28,'img/itemlogo/zhanShenLing.png','false',false,false,'img/itemlogo/zhanShenLing.png','fitems.pet.mm.ZhanShenLing_G',false,'0070DD',false,false,true,'战神令',false,false],['银元丹',38,'个',29,'img/itemlogo/YinYuanDan.gif','false',true,false,'img/itemlogo/YinYuanDan.gif','fitems.liandan.YinYuanDan',false,'008800',false,true,true,'银元丹',false,false],['手机充电券',19,'块',30,'img/itemlogo/shouji/ShouJiChongDianJuan.gif','false',true,false,'img/itemlogo/shouji/ShouJiChongDianJuan.gif','fitems.temp.ShouJiChongDianJuan',false,'008800',false,false,true,'手机充电券',false,false],['技能小黑本I',19,'个',31,'img/itemlogo/item/JiNengXiaoHeiBen.gif','false',true,false,'img/itemlogo/item/JiNengXiaoHeiBen.gif','fitems.experience.JiNengXiaoHeiBen1_G',false,'008800',false,false,true,'技能小黑本I',false,false],['金元丹',145,'个',32,'img/itemlogo/JinYuanDan.gif','false',true,false,'img/itemlogo/JinYuanDan.gif','fitems.liandan.JinYuanDan',false,'008800',false,true,true,'金元丹',false,false],['源质合金',19,'个',33,'img/itemlogo/BaseYuanZhiHeJin.gif','false',true,false,'img/itemlogo/BaseYuanZhiHeJin.gif','fitems.pet.mm.YuanZhiHeJin_G',false,'A335EE',false,false,true,'源质合金',false,false],['境界神丹',3,'个',34,'img/itemlogo/BaseJingJieShenDan.gif','false',true,false,'img/itemlogo/BaseJingJieShenDan.gif','fitems.pet.mm.JingJieShenDan_G',false,'A335EE',false,false,false,'境界神丹',false,false],['时光药水',18,'个',35,'img/itemlogo/shouhuling/ShiGuangYaoShui.gif','false',true,false,'img/itemlogo/shouhuling/ShiGuangYaoShui.gif','fitems.shouhuling.other.ShiGuangYaoShui_G',false,'008800',false,false,true,'时光药水',false,false],['英魂',36,'个',36,'img/huiji/BaseYingHun.gif','false',false,false,'img/huiji/BaseYingHun.gif','fitems.huiji.YingHun_G',false,'0070DD',false,true,true,'英魂',false,false],['练功房门卡',18,'张',37,'img/itemlogo/other/mm/RoomCard.gif','false',false,false,'img/itemlogo/other/mm/RoomCard.gif','fitems.pet.mm.DoExeRoomCard_G1',false,'0070DD',false,false,true,'练功房门卡',false,false],['泡泡图鉴',1,'张',38,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['猛虎之奇袭者战靴',1,'件',39,'img/arm/armor/mail/feet/MengHuQiXiZheZhanXue.gif','',false,false,'img/arm/armor/mail/feet/MengHuQiXiZheZhanXue.gif','fitems.arm.armor.mail.feet.lv014.uncommon.MengHuQiXiZheZhanXue',false,'008800',false,false,true,'猛虎之奇袭者战靴',false,false],['小型碧玉连珠',9,'个',40,'img/qixing/BaseLianZhu.gif','false',true,false,'img/qixing/BaseLianZhu.gif','fitems.qixing.BiYuLianZhu_Small',false,'008800',false,false,true,'小型碧玉连珠',false,false],['初级守护灵饲料',65,'个',41,'img/itemlogo/shouhuling/ShiWu.gif','false',true,true,'img/itemlogo/shouhuling/ShiWu.gif','fitems.shouhuling.other.ChuJiSiLiao',false,'000000',false,false,true,'初级守护灵饲料',false,false]],'<span class=sP>1002<img border=0 src=/img/itemlogo/jinb.gif>47<img border=0 src=/img/itemlogo/yinb.gif>99<img border=0 src=/img/itemlogo/tongb.gif></span>',225);</script><script>p.addCM("<font color=green> <u>小蓝追星族2</u> 从<u>竹叶青</u>尸体上获得: <font color=0070DD>初级升级石</font>x1[11:27]</font><br>");</script><script>p.addCM("<font color=green> <u>半夏ちミ忆素颜</u> 从<u>火魂</u>尸体上获得: <font color=0070DD>高级升级石</font>x1[11:27]</font><br>");</script><script>p.addCM("<font color=green> <u>卡蒂克</u> 从<u><font color=green>死神山羊</font></u>尸体上获得: <font color=0070DD>闪耀光辉之剑</font>x1[11:27]</font><br>");</script><script>p.addMessage('roomReader',"<!--thing--><font color=#C40000>卡蒂克所在的队伍成功击杀死神山羊，他们的功绩将记入史册!</font><br>",false);
    """

    # 性能测试
    start_time = time.perf_counter()
    results = parse_js_code(test_code)
    end_time = time.perf_counter()

    print(f"解析耗时: {(end_time - start_time) * 1000:.2f} 毫秒")
    print(f"解析到 {len(results)} 个函数调用")

    # 构建完整的结果数据
    output_data = {
        "parse_info": {
            "parse_time_ms": round((end_time - start_time) * 1000, 2),
            "total_functions": len(results),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        },
        "functions": results  # 现在是列表格式
    }

    # 保存为JSON文件
    with open('js_parse.json', 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)

    print(f"结果已保存到 js_parse.json 文件")

    # 演示列表取值的便利性
    print("\n列表取值示例:")
    if results:
        print(f"第1个函数名: {results[0]['function_name']}")
        print(f"第1个函数参数数量: {len(results[0]['parameters'])}")
        if len(results) > 1:
            print(f"第2个函数名: {results[1]['function_name']}")
        print(f"总共解析了 {len(results)} 个函数")

def func_test_package():
    # d = {'function_name': 'showI', 'parameters': [{'type': 'array', 'value': "[['传送石',1,'块',0,'img/itemlogo/other/ChuanSongShi.gif','false',true,true,'img/itemlogo/other/ChuanSongShi.gif','fitems.pet.other.ChuanSongShi',false,'000000',false,false,false,'传送石',false,false],['月光的祝福',4,'支',1,'img/itemlogo/yao/YueGuangZhuFu.gif','false',true,true,'img/itemlogo/yao/YueGuangZhuFu.gif','fitems.pet.huifu.fightstatus.YueGuang_1_G',false,'008800',false,false,true,'月光的祝福',false,false],['每曰二魔纹包(6)',1,'只',2,'img/itemlogo/item/budai.gif','false',true,false,'img/itemlogo/item/budai.gif','fitems.pet.other.Bag12',false,'000000',false,false,true,'每曰二魔纹包(6)',false,false],['竹蜻蜓',81,'支',3,'img/itemlogo/item/zhuqingting.gif','false',true,true,'img/itemlogo/item/zhuqingting.gif','fitems.pet.huifu.ZhuQingTing',false,'000000',false,false,true,'竹蜻蜓',false,false],['神符石',445,'枚',4,'img/shenfu/ShenFuShi_Lv.gif','false',true,false,'img/shenfu/ShenFuShi_Lv.gif','fitems.shenfu.ShenFuShi_Lv',false,'008800',false,false,true,'神符石',false,false],['小号材料丝绸包(5)',1,'只',5,'img/itemlogo/item/budai.gif','false',true,false,'img/itemlogo/item/budai.gif','fitems.pet.other.Bag8',false,'000000',false,false,true,'小号材料丝绸包(5)',false,false],['白色羽毛',99,'支',6,'img/itemlogo/item/baiyumao.gif','false',true,true,'img/itemlogo/item/baiyumao.gif','fitems.pet.huifu.BaiYuMao',false,'000000',false,false,true,'白色羽毛',false,false],['每曰魔纹包(8)',1,'只',7,'img/itemlogo/item/budai.gif','false',true,false,'img/itemlogo/item/budai.gif','fitems.pet.other.Bag12',false,'000000',false,false,true,'每曰魔纹包(8)',false,false],['秘银宝箱',203,'只',8,'img/itemlogo/item/Boxb.gif','false',true,false,'img/itemlogo/item/Boxb.gif','fitems.pet.other.BoxMiYin',false,'008800',false,false,true,'秘银宝箱',false,false],['苹果',727,'个',9,'img/itemlogo/stuff/apple.gif','false',true,true,'img/itemlogo/stuff/apple.gif','fitems.pet.complex.PingGuo',false,'000000',false,false,true,'苹果',false,false],['守护灵经验书I',302,'个',10,'img/itemlogo/shouhuling/SHLJingYanShu.gif','false',false,false,'img/itemlogo/shouhuling/SHLJingYanShu.gif','fitems.shouhuling.exp.SHLJingYanShu1',false,'000000',false,false,true,'守护灵经验书I',false,false],['昆虫外壳',427,'只',11,'img/itemlogo/stuff/kunchongwaike.gif','false',false,false,'img/itemlogo/stuff/kunchongwaike.gif','fitems.pet.complex.KunChongWaiKe',false,'000000',false,false,true,'昆虫外壳',false,false],['小型补血剂',74,'支',12,'img/bar/prop/HongPing.gif','false',true,true,'img/bar/prop/HongPing.gif','fitems.pet.huifu.HongPing1',false,'000000',false,false,true,'小型补血剂',false,false],['经验卷轴I',55,'卷',13,'img/itemlogo/item/ExpRollLv1.gif','false',true,false,'img/itemlogo/item/ExpRollLv1.gif','fitems.experience.ExpRollLv1',false,'000000',false,false,true,'经验卷轴I',false,false],['蜜蜂卡片',34,'张',14,'img/itemlogo/card.gif','false',true,false,'img/itemlogo/card.gif','fitems.card.xie.MiFeng',false,'000000',false,false,true,'蜜蜂卡片',false,false],['蚕蛹卡片',23,'张',15,'img/itemlogo/card.gif','false',true,false,'img/itemlogo/card.gif','fitems.card.dun.CanYong',false,'000000',false,false,true,'蚕蛹卡片',false,false],['泡泡图鉴',1,'张',16,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['钢筋铁骨1级技能果实',13,'个',17,'img/itemlogo/shouhuling/GuoShi.gif','false',false,false,'img/itemlogo/shouhuling/GuoShi.gif','fitems.shouhuling.other.GangJinTieGuGuoShilv1',false,'008800',false,false,true,'钢筋铁骨1级技能果实',false,false],['泡泡图鉴',1,'张',18,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['绿毛虫图鉴',1,'张',19,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['许愿绳',32,'根',20,'img/itemlogo/XuYuanSheng.gif','false',false,false,'img/itemlogo/XuYuanSheng.gif','fitems.xuyuanshu.XuYuanSheng',false,'737373',false,true,true,'许愿绳',false,false],['法宝精化石',16,'枚',21,'img/itemlogo/fabao/FaBaoJingHuaShi.gif','false',true,false,'img/itemlogo/fabao/FaBaoJingHuaShi.gif','fitems.fabao.FaBaoJingHuaShi_G',false,'008800',false,false,true,'法宝精化石',false,false],['初级升级石',25,'个',22,'img/itemlogo/UpgradeStoneBlue.gif','false',true,false,'img/itemlogo/UpgradeStoneBlue.gif','fitems.card.weapon.UpgradeStoneLv30Blue',false,'0070DD',false,false,true,'初级升级石',false,false],['牛鼻环',3,'只',23,'img/itemlogo/stuff/niubihuan.gif','false',false,false,'img/itemlogo/stuff/niubihuan.gif','fitems.pet.complex.NiuBiHuan',false,'000000',false,false,true,'牛鼻环',false,false],['短矛',1,'把',24,'img/arm/weapon/qiang/DuanMao.gif','',false,false,'img/arm/weapon/qiang/DuanMao.gif','fitems.arm.weapon.qiang.lv020.common.DuanMao',false,'737373',false,false,true,'短矛',false,false],['神通镜',1,'面',25,'img/itemlogo/task/ShenTongJing.gif','false',true,false,'img/itemlogo/task/ShenTongJing.gif','fitems.task.xueyuancheng.ShenTongJing',false,'000000',false,false,true,'神通镜',false,false],['银雪平原BOSS奇美拉的头',1,'颗',26,'img/itemlogo/task/QiMeiLaBossHead.gif','false',false,false,'img/itemlogo/task/QiMeiLaBossHead.gif','fitems.task.yinxuepingyuan.QiMeiLaBossHead',false,'000000',false,false,true,'银雪平原BOSS奇美拉的头',false,false],['世界碎片',1,'片',27,'img/itemlogo/other/YinYue_4.gif','false',false,false,'img/itemlogo/other/YinYue_4.gif','fitems.task.mopcity.ShiJieSuiPian',false,'000000',false,false,true,'世界碎片',false,false],['战神令',19,'只',28,'img/itemlogo/zhanShenLing.png','false',false,false,'img/itemlogo/zhanShenLing.png','fitems.pet.mm.ZhanShenLing_G',false,'0070DD',false,false,true,'战神令',false,false],['银元丹',38,'个',29,'img/itemlogo/YinYuanDan.gif','false',true,false,'img/itemlogo/YinYuanDan.gif','fitems.liandan.YinYuanDan',false,'008800',false,true,true,'银元丹',false,false],['手机充电券',19,'块',30,'img/itemlogo/shouji/ShouJiChongDianJuan.gif','false',true,false,'img/itemlogo/shouji/ShouJiChongDianJuan.gif','fitems.temp.ShouJiChongDianJuan',false,'008800',false,false,true,'手机充电券',false,false],['技能小黑本I',19,'个',31,'img/itemlogo/item/JiNengXiaoHeiBen.gif','false',true,false,'img/itemlogo/item/JiNengXiaoHeiBen.gif','fitems.experience.JiNengXiaoHeiBen1_G',false,'008800',false,false,true,'技能小黑本I',false,false],['金元丹',145,'个',32,'img/itemlogo/JinYuanDan.gif','false',true,false,'img/itemlogo/JinYuanDan.gif','fitems.liandan.JinYuanDan',false,'008800',false,true,true,'金元丹',false,false],['源质合金',19,'个',33,'img/itemlogo/BaseYuanZhiHeJin.gif','false',true,false,'img/itemlogo/BaseYuanZhiHeJin.gif','fitems.pet.mm.YuanZhiHeJin_G',false,'A335EE',false,false,true,'源质合金',false,false],['境界神丹',3,'个',34,'img/itemlogo/BaseJingJieShenDan.gif','false',true,false,'img/itemlogo/BaseJingJieShenDan.gif','fitems.pet.mm.JingJieShenDan_G',false,'A335EE',false,false,false,'境界神丹',false,false],['时光药水',18,'个',35,'img/itemlogo/shouhuling/ShiGuangYaoShui.gif','false',true,false,'img/itemlogo/shouhuling/ShiGuangYaoShui.gif','fitems.shouhuling.other.ShiGuangYaoShui_G',false,'008800',false,false,true,'时光药水',false,false],['英魂',36,'个',36,'img/huiji/BaseYingHun.gif','false',false,false,'img/huiji/BaseYingHun.gif','fitems.huiji.YingHun_G',false,'0070DD',false,true,true,'英魂',false,false],['练功房门卡',18,'张',37,'img/itemlogo/other/mm/RoomCard.gif','false',false,false,'img/itemlogo/other/mm/RoomCard.gif','fitems.pet.mm.DoExeRoomCard_G1',false,'0070DD',false,false,true,'练功房门卡',false,false],['泡泡图鉴',1,'张',38,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['猛虎之奇袭者战靴',1,'件',39,'img/arm/armor/mail/feet/MengHuQiXiZheZhanXue.gif','',false,false,'img/arm/armor/mail/feet/MengHuQiXiZheZhanXue.gif','fitems.arm.armor.mail.feet.lv014.uncommon.MengHuQiXiZheZhanXue',false,'008800',false,false,true,'猛虎之奇袭者战靴',false,false],['小型碧玉连珠',9,'个',40,'img/qixing/BaseLianZhu.gif','false',true,false,'img/qixing/BaseLianZhu.gif','fitems.qixing.BiYuLianZhu_Small',false,'008800',false,false,true,'小型碧玉连珠',false,false],['初级守护灵饲料',65,'个',41,'img/itemlogo/shouhuling/ShiWu.gif','false',true,true,'img/itemlogo/shouhuling/ShiWu.gif','fitems.shouhuling.other.ChuJiSiLiao',false,'000000',false,false,true,'初级守护灵饲料',false,false]]", 'raw': "[['传送石',1,'块',0,'img/itemlogo/other/ChuanSongShi.gif','false',true,true,'img/itemlogo/other/ChuanSongShi.gif','fitems.pet.other.ChuanSongShi',false,'000000',false,false,false,'传送石',false,false],['月光的祝福',4,'支',1,'img/itemlogo/yao/YueGuangZhuFu.gif','false',true,true,'img/itemlogo/yao/YueGuangZhuFu.gif','fitems.pet.huifu.fightstatus.YueGuang_1_G',false,'008800',false,false,true,'月光的祝福',false,false],['每曰二魔纹包(6)',1,'只',2,'img/itemlogo/item/budai.gif','false',true,false,'img/itemlogo/item/budai.gif','fitems.pet.other.Bag12',false,'000000',false,false,true,'每曰二魔纹包(6)',false,false],['竹蜻蜓',81,'支',3,'img/itemlogo/item/zhuqingting.gif','false',true,true,'img/itemlogo/item/zhuqingting.gif','fitems.pet.huifu.ZhuQingTing',false,'000000',false,false,true,'竹蜻蜓',false,false],['神符石',445,'枚',4,'img/shenfu/ShenFuShi_Lv.gif','false',true,false,'img/shenfu/ShenFuShi_Lv.gif','fitems.shenfu.ShenFuShi_Lv',false,'008800',false,false,true,'神符石',false,false],['小号材料丝绸包(5)',1,'只',5,'img/itemlogo/item/budai.gif','false',true,false,'img/itemlogo/item/budai.gif','fitems.pet.other.Bag8',false,'000000',false,false,true,'小号材料丝绸包(5)',false,false],['白色羽毛',99,'支',6,'img/itemlogo/item/baiyumao.gif','false',true,true,'img/itemlogo/item/baiyumao.gif','fitems.pet.huifu.BaiYuMao',false,'000000',false,false,true,'白色羽毛',false,false],['每曰魔纹包(8)',1,'只',7,'img/itemlogo/item/budai.gif','false',true,false,'img/itemlogo/item/budai.gif','fitems.pet.other.Bag12',false,'000000',false,false,true,'每曰魔纹包(8)',false,false],['秘银宝箱',203,'只',8,'img/itemlogo/item/Boxb.gif','false',true,false,'img/itemlogo/item/Boxb.gif','fitems.pet.other.BoxMiYin',false,'008800',false,false,true,'秘银宝箱',false,false],['苹果',727,'个',9,'img/itemlogo/stuff/apple.gif','false',true,true,'img/itemlogo/stuff/apple.gif','fitems.pet.complex.PingGuo',false,'000000',false,false,true,'苹果',false,false],['守护灵经验书I',302,'个',10,'img/itemlogo/shouhuling/SHLJingYanShu.gif','false',false,false,'img/itemlogo/shouhuling/SHLJingYanShu.gif','fitems.shouhuling.exp.SHLJingYanShu1',false,'000000',false,false,true,'守护灵经验书I',false,false],['昆虫外壳',427,'只',11,'img/itemlogo/stuff/kunchongwaike.gif','false',false,false,'img/itemlogo/stuff/kunchongwaike.gif','fitems.pet.complex.KunChongWaiKe',false,'000000',false,false,true,'昆虫外壳',false,false],['小型补血剂',74,'支',12,'img/bar/prop/HongPing.gif','false',true,true,'img/bar/prop/HongPing.gif','fitems.pet.huifu.HongPing1',false,'000000',false,false,true,'小型补血剂',false,false],['经验卷轴I',55,'卷',13,'img/itemlogo/item/ExpRollLv1.gif','false',true,false,'img/itemlogo/item/ExpRollLv1.gif','fitems.experience.ExpRollLv1',false,'000000',false,false,true,'经验卷轴I',false,false],['蜜蜂卡片',34,'张',14,'img/itemlogo/card.gif','false',true,false,'img/itemlogo/card.gif','fitems.card.xie.MiFeng',false,'000000',false,false,true,'蜜蜂卡片',false,false],['蚕蛹卡片',23,'张',15,'img/itemlogo/card.gif','false',true,false,'img/itemlogo/card.gif','fitems.card.dun.CanYong',false,'000000',false,false,true,'蚕蛹卡片',false,false],['泡泡图鉴',1,'张',16,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['钢筋铁骨1级技能果实',13,'个',17,'img/itemlogo/shouhuling/GuoShi.gif','false',false,false,'img/itemlogo/shouhuling/GuoShi.gif','fitems.shouhuling.other.GangJinTieGuGuoShilv1',false,'008800',false,false,true,'钢筋铁骨1级技能果实',false,false],['泡泡图鉴',1,'张',18,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['绿毛虫图鉴',1,'张',19,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['许愿绳',32,'根',20,'img/itemlogo/XuYuanSheng.gif','false',false,false,'img/itemlogo/XuYuanSheng.gif','fitems.xuyuanshu.XuYuanSheng',false,'737373',false,true,true,'许愿绳',false,false],['法宝精化石',16,'枚',21,'img/itemlogo/fabao/FaBaoJingHuaShi.gif','false',true,false,'img/itemlogo/fabao/FaBaoJingHuaShi.gif','fitems.fabao.FaBaoJingHuaShi_G',false,'008800',false,false,true,'法宝精化石',false,false],['初级升级石',25,'个',22,'img/itemlogo/UpgradeStoneBlue.gif','false',true,false,'img/itemlogo/UpgradeStoneBlue.gif','fitems.card.weapon.UpgradeStoneLv30Blue',false,'0070DD',false,false,true,'初级升级石',false,false],['牛鼻环',3,'只',23,'img/itemlogo/stuff/niubihuan.gif','false',false,false,'img/itemlogo/stuff/niubihuan.gif','fitems.pet.complex.NiuBiHuan',false,'000000',false,false,true,'牛鼻环',false,false],['短矛',1,'把',24,'img/arm/weapon/qiang/DuanMao.gif','',false,false,'img/arm/weapon/qiang/DuanMao.gif','fitems.arm.weapon.qiang.lv020.common.DuanMao',false,'737373',false,false,true,'短矛',false,false],['神通镜',1,'面',25,'img/itemlogo/task/ShenTongJing.gif','false',true,false,'img/itemlogo/task/ShenTongJing.gif','fitems.task.xueyuancheng.ShenTongJing',false,'000000',false,false,true,'神通镜',false,false],['银雪平原BOSS奇美拉的头',1,'颗',26,'img/itemlogo/task/QiMeiLaBossHead.gif','false',false,false,'img/itemlogo/task/QiMeiLaBossHead.gif','fitems.task.yinxuepingyuan.QiMeiLaBossHead',false,'000000',false,false,true,'银雪平原BOSS奇美拉的头',false,false],['世界碎片',1,'片',27,'img/itemlogo/other/YinYue_4.gif','false',false,false,'img/itemlogo/other/YinYue_4.gif','fitems.task.mopcity.ShiJieSuiPian',false,'000000',false,false,true,'世界碎片',false,false],['战神令',19,'只',28,'img/itemlogo/zhanShenLing.png','false',false,false,'img/itemlogo/zhanShenLing.png','fitems.pet.mm.ZhanShenLing_G',false,'0070DD',false,false,true,'战神令',false,false],['银元丹',38,'个',29,'img/itemlogo/YinYuanDan.gif','false',true,false,'img/itemlogo/YinYuanDan.gif','fitems.liandan.YinYuanDan',false,'008800',false,true,true,'银元丹',false,false],['手机充电券',19,'块',30,'img/itemlogo/shouji/ShouJiChongDianJuan.gif','false',true,false,'img/itemlogo/shouji/ShouJiChongDianJuan.gif','fitems.temp.ShouJiChongDianJuan',false,'008800',false,false,true,'手机充电券',false,false],['技能小黑本I',19,'个',31,'img/itemlogo/item/JiNengXiaoHeiBen.gif','false',true,false,'img/itemlogo/item/JiNengXiaoHeiBen.gif','fitems.experience.JiNengXiaoHeiBen1_G',false,'008800',false,false,true,'技能小黑本I',false,false],['金元丹',145,'个',32,'img/itemlogo/JinYuanDan.gif','false',true,false,'img/itemlogo/JinYuanDan.gif','fitems.liandan.JinYuanDan',false,'008800',false,true,true,'金元丹',false,false],['源质合金',19,'个',33,'img/itemlogo/BaseYuanZhiHeJin.gif','false',true,false,'img/itemlogo/BaseYuanZhiHeJin.gif','fitems.pet.mm.YuanZhiHeJin_G',false,'A335EE',false,false,true,'源质合金',false,false],['境界神丹',3,'个',34,'img/itemlogo/BaseJingJieShenDan.gif','false',true,false,'img/itemlogo/BaseJingJieShenDan.gif','fitems.pet.mm.JingJieShenDan_G',false,'A335EE',false,false,false,'境界神丹',false,false],['时光药水',18,'个',35,'img/itemlogo/shouhuling/ShiGuangYaoShui.gif','false',true,false,'img/itemlogo/shouhuling/ShiGuangYaoShui.gif','fitems.shouhuling.other.ShiGuangYaoShui_G',false,'008800',false,false,true,'时光药水',false,false],['英魂',36,'个',36,'img/huiji/BaseYingHun.gif','false',false,false,'img/huiji/BaseYingHun.gif','fitems.huiji.YingHun_G',false,'0070DD',false,true,true,'英魂',false,false],['练功房门卡',18,'张',37,'img/itemlogo/other/mm/RoomCard.gif','false',false,false,'img/itemlogo/other/mm/RoomCard.gif','fitems.pet.mm.DoExeRoomCard_G1',false,'0070DD',false,false,true,'练功房门卡',false,false],['泡泡图鉴',1,'张',38,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['猛虎之奇袭者战靴',1,'件',39,'img/arm/armor/mail/feet/MengHuQiXiZheZhanXue.gif','',false,false,'img/arm/armor/mail/feet/MengHuQiXiZheZhanXue.gif','fitems.arm.armor.mail.feet.lv014.uncommon.MengHuQiXiZheZhanXue',false,'008800',false,false,true,'猛虎之奇袭者战靴',false,false],['小型碧玉连珠',9,'个',40,'img/qixing/BaseLianZhu.gif','false',true,false,'img/qixing/BaseLianZhu.gif','fitems.qixing.BiYuLianZhu_Small',false,'008800',false,false,true,'小型碧玉连珠',false,false],['初级守护灵饲料',65,'个',41,'img/itemlogo/shouhuling/ShiWu.gif','false',true,true,'img/itemlogo/shouhuling/ShiWu.gif','fitems.shouhuling.other.ChuJiSiLiao',false,'000000',false,false,true,'初级守护灵饲料',false,false]]"}, {'type': 'string', 'value': '10024799', 'raw': "'10024799'"}, {'type': 'integer', 'value': 225, 'raw': '225'}], 'raw_call': "p.showI([['传送石',1,'块',0,'img/itemlogo/other/ChuanSongShi.gif','false',true,true,'img/itemlogo/other/ChuanSongShi.gif','fitems.pet.other.ChuanSongShi',false,'000000',false,false,false,'传送石',false,false],['月光的祝福',4,'支',1,'img/itemlogo/yao/YueGuangZhuFu.gif','false',true,true,'img/itemlogo/yao/YueGuangZhuFu.gif','fitems.pet.huifu.fightstatus.YueGuang_1_G',false,'008800',false,false,true,'月光的祝福',false,false],['每曰二魔纹包(6)',1,'只',2,'img/itemlogo/item/budai.gif','false',true,false,'img/itemlogo/item/budai.gif','fitems.pet.other.Bag12',false,'000000',false,false,true,'每曰二魔纹包(6)',false,false],['竹蜻蜓',81,'支',3,'img/itemlogo/item/zhuqingting.gif','false',true,true,'img/itemlogo/item/zhuqingting.gif','fitems.pet.huifu.ZhuQingTing',false,'000000',false,false,true,'竹蜻蜓',false,false],['神符石',445,'枚',4,'img/shenfu/ShenFuShi_Lv.gif','false',true,false,'img/shenfu/ShenFuShi_Lv.gif','fitems.shenfu.ShenFuShi_Lv',false,'008800',false,false,true,'神符石',false,false],['小号材料丝绸包(5)',1,'只',5,'img/itemlogo/item/budai.gif','false',true,false,'img/itemlogo/item/budai.gif','fitems.pet.other.Bag8',false,'000000',false,false,true,'小号材料丝绸包(5)',false,false],['白色羽毛',99,'支',6,'img/itemlogo/item/baiyumao.gif','false',true,true,'img/itemlogo/item/baiyumao.gif','fitems.pet.huifu.BaiYuMao',false,'000000',false,false,true,'白色羽毛',false,false],['每曰魔纹包(8)',1,'只',7,'img/itemlogo/item/budai.gif','false',true,false,'img/itemlogo/item/budai.gif','fitems.pet.other.Bag12',false,'000000',false,false,true,'每曰魔纹包(8)',false,false],['秘银宝箱',203,'只',8,'img/itemlogo/item/Boxb.gif','false',true,false,'img/itemlogo/item/Boxb.gif','fitems.pet.other.BoxMiYin',false,'008800',false,false,true,'秘银宝箱',false,false],['苹果',727,'个',9,'img/itemlogo/stuff/apple.gif','false',true,true,'img/itemlogo/stuff/apple.gif','fitems.pet.complex.PingGuo',false,'000000',false,false,true,'苹果',false,false],['守护灵经验书I',302,'个',10,'img/itemlogo/shouhuling/SHLJingYanShu.gif','false',false,false,'img/itemlogo/shouhuling/SHLJingYanShu.gif','fitems.shouhuling.exp.SHLJingYanShu1',false,'000000',false,false,true,'守护灵经验书I',false,false],['昆虫外壳',427,'只',11,'img/itemlogo/stuff/kunchongwaike.gif','false',false,false,'img/itemlogo/stuff/kunchongwaike.gif','fitems.pet.complex.KunChongWaiKe',false,'000000',false,false,true,'昆虫外壳',false,false],['小型补血剂',74,'支',12,'img/bar/prop/HongPing.gif','false',true,true,'img/bar/prop/HongPing.gif','fitems.pet.huifu.HongPing1',false,'000000',false,false,true,'小型补血剂',false,false],['经验卷轴I',55,'卷',13,'img/itemlogo/item/ExpRollLv1.gif','false',true,false,'img/itemlogo/item/ExpRollLv1.gif','fitems.experience.ExpRollLv1',false,'000000',false,false,true,'经验卷轴I',false,false],['蜜蜂卡片',34,'张',14,'img/itemlogo/card.gif','false',true,false,'img/itemlogo/card.gif','fitems.card.xie.MiFeng',false,'000000',false,false,true,'蜜蜂卡片',false,false],['蚕蛹卡片',23,'张',15,'img/itemlogo/card.gif','false',true,false,'img/itemlogo/card.gif','fitems.card.dun.CanYong',false,'000000',false,false,true,'蚕蛹卡片',false,false],['泡泡图鉴',1,'张',16,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['钢筋铁骨1级技能果实',13,'个',17,'img/itemlogo/shouhuling/GuoShi.gif','false',false,false,'img/itemlogo/shouhuling/GuoShi.gif','fitems.shouhuling.other.GangJinTieGuGuoShilv1',false,'008800',false,false,true,'钢筋铁骨1级技能果实',false,false],['泡泡图鉴',1,'张',18,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['绿毛虫图鉴',1,'张',19,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['许愿绳',32,'根',20,'img/itemlogo/XuYuanSheng.gif','false',false,false,'img/itemlogo/XuYuanSheng.gif','fitems.xuyuanshu.XuYuanSheng',false,'737373',false,true,true,'许愿绳',false,false],['法宝精化石',16,'枚',21,'img/itemlogo/fabao/FaBaoJingHuaShi.gif','false',true,false,'img/itemlogo/fabao/FaBaoJingHuaShi.gif','fitems.fabao.FaBaoJingHuaShi_G',false,'008800',false,false,true,'法宝精化石',false,false],['初级升级石',25,'个',22,'img/itemlogo/UpgradeStoneBlue.gif','false',true,false,'img/itemlogo/UpgradeStoneBlue.gif','fitems.card.weapon.UpgradeStoneLv30Blue',false,'0070DD',false,false,true,'初级升级石',false,false],['牛鼻环',3,'只',23,'img/itemlogo/stuff/niubihuan.gif','false',false,false,'img/itemlogo/stuff/niubihuan.gif','fitems.pet.complex.NiuBiHuan',false,'000000',false,false,true,'牛鼻环',false,false],['短矛',1,'把',24,'img/arm/weapon/qiang/DuanMao.gif','',false,false,'img/arm/weapon/qiang/DuanMao.gif','fitems.arm.weapon.qiang.lv020.common.DuanMao',false,'737373',false,false,true,'短矛',false,false],['神通镜',1,'面',25,'img/itemlogo/task/ShenTongJing.gif','false',true,false,'img/itemlogo/task/ShenTongJing.gif','fitems.task.xueyuancheng.ShenTongJing',false,'000000',false,false,true,'神通镜',false,false],['银雪平原BOSS奇美拉的头',1,'颗',26,'img/itemlogo/task/QiMeiLaBossHead.gif','false',false,false,'img/itemlogo/task/QiMeiLaBossHead.gif','fitems.task.yinxuepingyuan.QiMeiLaBossHead',false,'000000',false,false,true,'银雪平原BOSS奇美拉的头',false,false],['世界碎片',1,'片',27,'img/itemlogo/other/YinYue_4.gif','false',false,false,'img/itemlogo/other/YinYue_4.gif','fitems.task.mopcity.ShiJieSuiPian',false,'000000',false,false,true,'世界碎片',false,false],['战神令',19,'只',28,'img/itemlogo/zhanShenLing.png','false',false,false,'img/itemlogo/zhanShenLing.png','fitems.pet.mm.ZhanShenLing_G',false,'0070DD',false,false,true,'战神令',false,false],['银元丹',38,'个',29,'img/itemlogo/YinYuanDan.gif','false',true,false,'img/itemlogo/YinYuanDan.gif','fitems.liandan.YinYuanDan',false,'008800',false,true,true,'银元丹',false,false],['手机充电券',19,'块',30,'img/itemlogo/shouji/ShouJiChongDianJuan.gif','false',true,false,'img/itemlogo/shouji/ShouJiChongDianJuan.gif','fitems.temp.ShouJiChongDianJuan',false,'008800',false,false,true,'手机充电券',false,false],['技能小黑本I',19,'个',31,'img/itemlogo/item/JiNengXiaoHeiBen.gif','false',true,false,'img/itemlogo/item/JiNengXiaoHeiBen.gif','fitems.experience.JiNengXiaoHeiBen1_G',false,'008800',false,false,true,'技能小黑本I',false,false],['金元丹',145,'个',32,'img/itemlogo/JinYuanDan.gif','false',true,false,'img/itemlogo/JinYuanDan.gif','fitems.liandan.JinYuanDan',false,'008800',false,true,true,'金元丹',false,false],['源质合金',19,'个',33,'img/itemlogo/BaseYuanZhiHeJin.gif','false',true,false,'img/itemlogo/BaseYuanZhiHeJin.gif','fitems.pet.mm.YuanZhiHeJin_G',false,'A335EE',false,false,true,'源质合金',false,false],['境界神丹',3,'个',34,'img/itemlogo/BaseJingJieShenDan.gif','false',true,false,'img/itemlogo/BaseJingJieShenDan.gif','fitems.pet.mm.JingJieShenDan_G',false,'A335EE',false,false,false,'境界神丹',false,false],['时光药水',18,'个',35,'img/itemlogo/shouhuling/ShiGuangYaoShui.gif','false',true,false,'img/itemlogo/shouhuling/ShiGuangYaoShui.gif','fitems.shouhuling.other.ShiGuangYaoShui_G',false,'008800',false,false,true,'时光药水',false,false],['英魂',36,'个',36,'img/huiji/BaseYingHun.gif','false',false,false,'img/huiji/BaseYingHun.gif','fitems.huiji.YingHun_G',false,'0070DD',false,true,true,'英魂',false,false],['练功房门卡',18,'张',37,'img/itemlogo/other/mm/RoomCard.gif','false',false,false,'img/itemlogo/other/mm/RoomCard.gif','fitems.pet.mm.DoExeRoomCard_G1',false,'0070DD',false,false,true,'练功房门卡',false,false],['泡泡图鉴',1,'张',38,'img/itemlogo/stuff/TuJian.gif','false',true,false,'img/itemlogo/stuff/TuJian.gif','fitems.card.tujian.TuJianKaPian',false,'008800',false,false,true,'图鉴卡片',false,false],['猛虎之奇袭者战靴',1,'件',39,'img/arm/armor/mail/feet/MengHuQiXiZheZhanXue.gif','',false,false,'img/arm/armor/mail/feet/MengHuQiXiZheZhanXue.gif','fitems.arm.armor.mail.feet.lv014.uncommon.MengHuQiXiZheZhanXue',false,'008800',false,false,true,'猛虎之奇袭者战靴',false,false],['小型碧玉连珠',9,'个',40,'img/qixing/BaseLianZhu.gif','false',true,false,'img/qixing/BaseLianZhu.gif','fitems.qixing.BiYuLianZhu_Small',false,'008800',false,false,true,'小型碧玉连珠',false,false],['初级守护灵饲料',65,'个',41,'img/itemlogo/shouhuling/ShiWu.gif','false',true,true,'img/itemlogo/shouhuling/ShiWu.gif','fitems.shouhuling.other.ChuJiSiLiao',false,'000000',false,false,true,'初级守护灵饲料',false,false]],'10024799',225);", 'end_pos': 8146}
    # print(ret_packages(d))
    s = """<script>p.showAllotWin(p.allotObj(58471,'<font color=000000>怨灵魂魄</font>','img/itemlogo/hunpo/Lv_1_b.gif','10只 <font color=000000>怨灵魂魄</font> 可合成1只 <font color=008800>怨灵魂魄精华</font><Br>'));</script>"""
    s = parse_js_code(s)[0]['parameters'][0]['value']
    print(re.findall(r'p\.allotObj\((\d+)', s))

# JS处理结束

def 获取验证码(proxies=None):
    """
    return X-TEMP_INT
    """
    # 取随机验证码尾数
    while True:
        temp_int = random.randint(2222, 8888)
        # 获取随机验证码
        # requests.get(f'http://randimg.gc.imop.com/com/randimg/img_plus.php?rnd_seed=2345',proxies=None,timeout=5).content
        try:
            yzm_data = requests.get(f'http://randimg.gc.imop.com/com/randimg/img_plus.php?rnd_seed={temp_int}', proxies=proxies).content
            # print(yzm_data)
        # 打码自己的服务器获取登录验证码
            response = requests.post(全局_验证码接口, data=base64.b64encode(yzm_data))
            # print(response.text)
        # 组合计算结果以及原始验证码尾数
            _res = json.loads(response.text).get('res')
            # print(_res)
            return str(_res) + str(temp_int)
        except Exception as e:
            return '0' + str(temp_int)

def 通用_发送邮件(receiver: str, content):
    smtp_server = "smtp.qq.com"
    sender_email = "<EMAIL>"
    # 构建邮件对象
    msg = MIMEMultipart()
    msg['From'] = '<EMAIL>'
    msg["To"] = receiver
    msg["Subject"] = '小叮当-极-提醒邮件'
    body = f'{(datetime.datetime.now()+ datetime.timedelta(days=-1)).strftime("%Y-%m-%d")}\n' + ' '.join(str(arg) for arg in content)
    msg.attach(MIMEText(body, 'plain'))
    server = smtplib.SMTP(smtp_server, 587)
    server.starttls()
    server.login(sender_email, 'eidkyaigsbgdbabg')
    server.sendmail(sender_email, receiver, msg.as_string())
    server.quit()

def 通用_获取字符串时间():
    return datetime.datetime.now().strftime('%H:%M:%S')

class MYJBase:
    def __init__(self,configs):
        self.版本 = '最低在线版'
        self.版本号 = '1.0.0'
        self.登录配置 = {
            '区服': configs.get('区服', 'x12'),
            '账号': configs.get('账号', 'wk93qlyy1'),
            '密码': configs.get('密码', '00000000'),
            '角色序号': configs.get('角色序号', '0'),
        }
        self.全局配置 = {
            '延时': configs.get('延时', 0.5),
            '非操作延时': configs.get('非操作延时', 1.5),
            '邮箱': configs.get('邮箱', '<EMAIL>'),
            '百分比经验上限': configs.get('百分比经验上限', 100),
            '日志输出': configs.get('日志输出', True),
            '在线控制器': configs.get('在线控制器', True),
            '重连时间': configs.get('重连时间', 300),
        }
        self.日常开关 = {
            '拖把每日': configs.get('拖把每日', False),
            '在线礼包': configs.get('在线礼包', False),
            '竞技场': configs.get('竞技场', False),
            '魔化': configs.get('魔化', False),
            '如意': configs.get('如意', False),
            '乐园': configs.get('乐园', False),
            '沼泽': configs.get('沼泽', False),
            '诅咒': configs.get('诅咒', False),
        }
        self.活动开关 = {
            '无双': configs.get('无双', False),
            '逆无双': configs.get('逆无双', False),
            '通天': configs.get('通天', False),
            '罗汉': configs.get('罗汉', False)
        }
        self.角色信息 = {
            '角色名': str,
            '角色ID': str,
            '等级': int,
            '地图': str,
            '房间': str,
            'hp_left': int,
            'hp_max': int,
            'sp_left': int,
            'sp_max': int,
            '经验百分比': int,
            'myUserId': str,
            'validateParam': str,
            '战斗状态': bool
        }
        self.网络信息 = {
            'cookies': None
        }
        self.地址 = {
            '角色地址': str,
            '保持在线地址': str,
            '命令地址': f'http://{self.登录配置["区服"]}.pet.imop.com/action.jsp?'
        }
        self.正则 = {
            'npc': re.compile(r"p\._getNpc\(([^)]*)\)"),
            'modify': re.compile(r'<[A-Za-z0-9 ="\':;/_\-.]*>'),
            'petLv': re.compile(r"petLv=(\d+);"),   # 宠物等级
            'nowMap': re.compile(r'nowMap\s*=\s*"([^"]+)"'),    # 当前所在地图
            'room': re.compile(r'room\s*=\s*"([^"]+)"'),    # 当前所在房间
            'hp_left': re.compile(r"<span id='hpLine_left_no'[^>]*>(\d+)</span>"),  # 剩余hp
            'hp_max': re.compile(r"<span id='hpLine_left_max_no'[^>]*>(\d+)</span>"),   # 最大hp
            'sp_left': re.compile(r"<span id='mpLine_left_no'[^>]*>(\d+)</span>"),  # 剩余sp
            'sp_max': re.compile(r"<span id='mpLine_left_max_no'>(\d+)</span>"),    # 最大sp
            'exp': re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE),   # 百分比经验
            'join_role': re.compile(r"selectimg\(\s*(\d+)\s*,\s*'click'\s*,\s*(\d+)[^)]*\)\s*.*?"
                                    r'<div\s+align="center"\s+style="position:relative;top:-10px">(.*?)</div>', re.DOTALL),  # 获取角色代码及序号部分
            # 'skills': re.compile(r"p\.cutArray\[(?:[0-5])\]='(perform.*?)';"),  #
            'petName': re.compile(r'petName\s?=\s?"(.*)";'),    # 宠物名
            'myUserId': re.compile(r'myUserId\s?=\s?"(.*)";'),  # id
            'validateParam': re.compile(r'validateParam\s?=\s?"(.*)";'),    # 链接验证参数
            'combat_start': re.compile(r"p\._combat\s*\(", re.IGNORECASE),      # 进入战斗
            'combat_end': re.compile(r"p\.lost\(\s*p\.petWin\.fighter_2\s*\)", re.IGNORECASE),  # 退出战斗
        }
        self.时间 = {
            '进战': None,
            '出战': None,
        }
        self.状态 = {
            '自己': list,
            '对手': list
        }
        self.消息 = {
            '右上': [],
            '左下': [],
            '右下': [],
            'NPC对话': [],
            '动作': [],
        }
        self.背包 = {
            '背包道具': [],
            '是否使用': bool
        }
        self.道具说明 = {
            '内容': dict,
            '是否使用': bool,
        }
        self.技能 = {
            '列表': None,
            '是否使用': bool
        }
        self.任务 = {
            '列表': None,
            '是否使用': False,
        }
        self.NPC = {
            '原始': [],
            '可攻击': []
        }
        self.客户端 = httpx.AsyncClient(timeout=None)
        self.命令线程锁 = Lock()
        self.在线线程 = None
        self.在线状态 = False

    def 控制台日志(self, *args):
        if self.全局配置['日志输出']:
            text = ' '.join(str(arg) for arg in args)
            print(f"{self.登录配置['区服']}=>{self.角色信息['角色名']}==>{text}")

    def 刷新角色信息(self):
        while True:
            response = requests.get(url=f"http://{self.登录配置['区服']}.pet.imop.com/pet.jsp?petid={self.角色信息['角色ID']}", cookies=self.网络信息['cookies'])
            t = response.text
            try:
                self.角色信息['等级'] = self.正则['petLv'].search(t).group(1)
                self.角色信息['地图'] = self.正则['nowMap'].search(t).group(1)
                if self.角色信息['地图'] is None:
                    continue
                self.角色信息['房间'] = self.正则['room'].search(t).group(1)
                if self.角色信息['地图'] is None:
                    continue
                self.角色信息['角色名'] = self.正则['petName'].search(t).group(1)
                self.角色信息['myUserId'] = self.正则['myUserId'].search(t).group(1)
                self.角色信息['validateParam'] = self.正则['validateParam'].search(t).group(1)
                self.角色信息['hp_left'] = self.正则['hp_left'].search(t).group(1)
                self.角色信息['sp_left'] = self.正则['sp_left'].search(t).group(1)
                self.角色信息['hp_max'] = self.正则['hp_max'].search(t).group(1)
                self.角色信息['sp_max'] = self.正则['sp_max'].search(t).group(1)
                self.角色信息['经验百分比'] = int(re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE).search(t).group(1))
                if self.角色信息['经验百分比'] >= self.全局配置['百分比经验上限']:
                    self.控制台日志(f"经验达到上限{self.全局配置['百分比经验上限']}%, 卡住经验")
                    通用_发送邮件(self.全局配置['邮箱'], f"账号{self.登录配置['账号']}角色{self.角色信息['角色名']}经验达到上限{self.全局配置['百分比经验上限']}%, 卡住经验")
                    #TODO 加入命令锁,锁死命令
                self.地址['保持在线地址'] = f"http://{self.登录配置['区服']}.pet.imop.com:8080/io/{self.角色信息['myUserId']}&{self.角色信息['validateParam']}"
                self.控制台日志('刷新角色属性完成')
                return
            except:
                pass

    def 保持在线(self):

        def _确认服务器是否维护完成():
            while True:
                res = requests.get(f"http://{self.登录配置['区服']}.pet.imop.com")
                if res.status_code == 200:
                    self.控制台日志(f"服务器{self.登录配置['区服']}不在维护,可登录")
                    return True
                else:
                    # self.控制台日志(res.status_code)
                    self.控制台日志(f"服务器{self.登录配置['区服']}状态非正常,返回状态码{res.status_code},等待继续检测")
                    time.sleep(2)

        def _登录():
            _error_code = {
                '0': "登录失败,请重新登录",
                '1': "签名验证失败",
                '2': "时间戳过期",
                '3': "参数为空或格式不正确",
                '4': "用户名密码验证未通过",
                '5': "用户已被锁定",
                '6': "密保未通过",
                '7': "cookie验证未通过",
                '8': "token验证未通过",
                '9': "大区验证未通过",
                '11': "验证码错误",
                '12': "验证码为空",
                '999': "系统异常，登录失败"
            }
            while True:
            # 登录发送信息
                login_post = {
                    'url': f"http://{self.登录配置['区服']}.pet.imop.com/LoginAction.jsp",
                    'cookies': {'mopet_logon': '123'},
                    'headers': {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': f"http://{self.登录配置['区服']}.pet.imop.com",
                        'Referer': f"http://{self.登录配置['区服']}.pet.imop.com/login.html",
                        'Connection': 'keep-alive',
                        'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                    },
                    'data': {
                        'user_name': quote(self.登录配置['账号'], encoding="gbk"),
                        'password': self.登录配置['密码'],
                        'checkcode': 获取验证码()
                    },
                }
                # 登录返回信息
                login_response = requests.post(**login_post)
                if r'document.location="/pet.jsp"' in login_response.text:
                    # print(login_response.text)
                    self.网络信息['cookies'] = login_response.cookies
                    self.控制台日志('登陆成功')
                    return True
                else:
                    for _code in _error_code.keys():
                        if f'errCode={_code}"' in login_response.text:
                            if _code == '11':
                                self.控制台日志('错误码为11,验证码错误,重新获取验证码登录')
                                continue
                            else:
                                self.控制台日志(f'出现非验证码错误,错误类型为=>{_error_code[_code]}')
                                return False

        def _进入角色():
            局_请求信息 = {
                'cookies': self.网络信息['cookies'],
                'headers': {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': f"http://{self.登录配置['区服']}.pet.imop.com",
                        'Referer': f"http://{self.登录配置['区服']}.pet.imop.com/login.html",
                        'Connection': 'keep-alive',
                        'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                },
            }
            while True:
                局_请求信息['url'] = f"http://{self.登录配置['区服']}.pet.imop.com/pet.jsp"
                局_登录请求返回对象 = requests.get(**局_请求信息)
                if 'selectimg' in 局_登录请求返回对象.text:
                    self.控制台日志('当前没有在游戏内的角色')
                    局_角色列表 = self.正则['join_role'].findall(局_登录请求返回对象.text)
                    if 局_角色列表:
                        # 打印所有角色信息
                        for 局_角色信息 in 局_角色列表:
                            self.控制台日志(f"""角色序号[{局_角色信息[0]}]角色名[{局_角色信息[2]}]角色id[{局_角色信息[1]}]""")
                        for 局_角色信息 in 局_角色列表:
                            if int(局_角色信息[0]) == int(self.登录配置['角色序号']):
                                self.角色信息['角色名'] = 局_角色信息[2]
                                self.角色信息['角色ID'] = 局_角色信息[1]
                                self.地址['角色地址'] = f"http://{self.登录配置['区服']}.pet.imop.com/pet.jsp?petId={self.角色信息['角色ID']}"
                                self.控制台日志(f'角色获取成功,目标角色名为{self.角色信息["角色名"]}ID为{self.角色信息["角色ID"]}')
                                while True:
                                    局_进入角色请求信息 = {
                                        'url': f"http://{self.登录配置['区服']}.pet.imop.com/pet.jsp?petid={self.角色信息['角色ID']}",
                                        'cookies': self.网络信息['cookies'],
                                    }
                                    局_进入角色请求返回对象 = requests.get(**局_进入角色请求信息)
                                    if 'showWorldMap' in 局_进入角色请求返回对象.text:
                                        break
                                self.控制台日志('进入角色成功')
                                self.刷新角色信息()
                                return True
                elif 'validateParam' in 局_登录请求返回对象.text:
                    self.控制台日志('当前已经进入角色')
                    self.角色信息['等级'] = self.正则['petLv'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['地图'] = self.正则['nowMap'].search(局_登录请求返回对象.text).group(1)
                    if self.角色信息['地图'] is None:
                        continue
                    self.角色信息['房间'] = self.正则['room'].search(局_登录请求返回对象.text).group(1)
                    if self.角色信息['地图'] is None:
                        continue
                    self.角色信息['角色名'] = self.正则['petName'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['myUserId'] = self.正则['myUserId'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['validateParam'] = self.正则['validateParam'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['hp_left'] = self.正则['hp_left'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['sp_left'] = self.正则['sp_left'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['hp_max'] = self.正则['hp_max'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['sp_max'] = self.正则['sp_max'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['经验百分比'] = int(re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE).search(局_登录请求返回对象.text).group(1))
                    if self.角色信息['经验百分比'] >= self.全局配置['百分比经验上限']:
                        self.控制台日志(f"经验达到上限{self.全局配置['百分比经验上限']}%, 卡住经验")
                        通用_发送邮件(self.全局配置['邮箱'], f"账号{self.登录配置['账号']}角色{self.角色信息['角色名']}经验达到上限{self.全局配置['百分比经验上限']}%, 卡住经验")
                        #TODO 加入命令锁,锁死命令
                    self.地址['保持在线地址']  = f"http://{self.登录配置['区服']}.pet.imop.com:8080/io/{self.角色信息['myUserId']}&{self.角色信息['validateParam']}"
                    self.控制台日志('刷新角色属性完成')
                    return True
                else:
                    self.控制台日志('未处理情况')
                    self.控制台日志(局_登录请求返回对象.text)
                    return False
        
        async def _异步在线():
            # 外层无限循环
            while True:
                while self.全局配置['在线控制器'] is False:
                    self.控制台日志('在线控制器已关闭,暂不登录')
                    await asyncio.sleep(3)
                if _确认服务器是否维护完成() and _登录():
                    if _进入角色():
                        try:
                            async with self.客户端.stream("GET", url=self.地址['保持在线地址'], cookies=self.网络信息['cookies']) as response:
                                self.在线状态 = True
                                async for line in response.aiter_text():
                                    _function_list = parse_js_code(line)# 解析js代码
                                    for _func in _function_list:
                                        # print(_func)
                                        if _func['function_name'] in [
                                                # 不处理函数列表
                                                'initWorker',   # 初始化函数
                                                'cls',          # 清空房间
                                                'cps',          # 清空房间
                                                'offOpenWin',   # 关闭窗口
                                                'clsMes',       # Npc对话框清空
                                                '_roomDesc',     # 房间描述
                                                'reOnlineNum',  # 在线状态
                                                'addUser',      # 当前房间增加角色
                                                'delUser',      # 当前房间删除角色
                                                'showRen',      # Npc图片
                                                'closeRen',     # Npc图片
                                                'showAlert',    # 白底小提示框
                                                'win',          # 战斗胜利,但是太快反应会卡指令,所以不作为结束战斗依据
                                                'closeBossZhanDouInfo',     # 疑似战斗结束,但是不能用,同上
                                                # '_showMiracle', # 无关痛痒,不知道是啥
                                                'showRenPic',   # NPC图片
                                                'showRenBigPic',   # NPC图片
                                                '_look',        # 被人观察,不处理
                                                'lost',         # 伪脱战,不处理
                                                'showLiLian',   # 历练,没必要处理
                                            ]:
                                            continue
                                        elif _func['function_name'] == '_combat':
                                            self.角色信息['战斗状态'] = True
                                            self.时间['进战'] = time.time()
                                            self.控制台日志('进入战斗')
                                        elif _func['function_name'] in ['_showMiracle']:
                                            self.角色信息['战斗状态'] = False
                                            self.状态['自己'] = []    # 脱战清空状态
                                            self.状态['对手'] = []    # 脱战清空状态
                                        elif _func['function_name'] == 'addCM':
                                            局_信息 = 通用_获取字符串时间() + '=>' + _func['parameters'][0]['value']
                                            self.消息['右上'].append(局_信息)
                                            self.控制台日志(f'CM右上框:{局_信息}')
                                            if len(self.消息['右上']) > 100:
                                                self.消息['右上'].pop(0)
                                        elif _func['function_name'] == 'addRM':
                                            局_信息 = 通用_获取字符串时间() + '=>' + _func['parameters'][0]['value']
                                            self.消息['左下'].append(局_信息)
                                            self.控制台日志(f'RM左下框:{局_信息}')
                                            if len(self.消息['左下']) > 100:
                                                self.消息['左下'].pop(0)
                                        elif _func['function_name'] == 'addMY':
                                            局_信息 = 通用_获取字符串时间() + '=>' + _func['parameters'][0]['value']
                                            self.消息['右下'].append(局_信息)
                                            self.控制台日志(f'MY右下框:{局_信息}')
                                            if len(self.消息['右下']) > 100:
                                                self.消息['右下'].pop(0)
                                        elif _func['function_name'] == 'addMessage':
                                            if _func['parameters'][0]['value'] == 'roomReader':
                                                局_信息 = 通用_获取字符串时间() + '=>' + _func["parameters"][1]["value"]
                                                if '无法施展' in 局_信息:
                                                    self.角色信息['战斗状态'] = False
                                                    self.状态['自己'] = []
                                                    self.状态['对手'] = []
                                                if '你向' in 局_信息 and '发起攻击!' in 局_信息:
                                                    self.角色信息['战斗状态'] = True
                                                    self.时间['进战'] = time.time()
                                                    self.控制台日志('进入战斗')
                                                self.控制台日志(f'Message左下框:{局_信息}')
                                                self.消息['左下'].append(局_信息)
                                                if len(self.消息['左下']) > 100:
                                                    self.消息['左下'].pop(0)
                                            else:
                                                self.控制台日志('待处理addMessage')
                                                通用_发送邮件('<EMAIL>', _func)
                                        elif _func['function_name'] == '_showFightStatus':
                                            if _func['parameters'][0]['value'] == 'fighter_1_status':
                                                self.状态['自己'] = _func['parameters'][1]['value']
                                                self.控制台日志(f"战斗-自身状态:{self.状态['自己']}")
                                            if _func['parameters'][0]['value'] == 'fighter_2_status':
                                                self.状态['对手'] = _func['parameters'][1]['value']
                                                self.控制台日志(f"战斗-自身状态:{self.状态['对手']}")
                                        elif _func['function_name'] == 'state':
                                            self.控制台日志('战斗相关,暂不处理')
                                            self.角色信息['战斗状态'] = True
                                        elif _func['function_name']  in ['att1', 'att2']:
                                            self.角色信息['战斗状态'] = True
                                            self.控制台日志('战斗相关,暂不处理')
                                        elif _func['function_name'] == 'addNpcs':
                                            _s = _func['parameters'][0]['value']
                                            self.NPC['原始'] = [i.replace("'", '').replace(" ", '').split(',') for i in self.正则['npc'].findall(_s)]
                                            self.NPC['可攻击']= [i for i in self.NPC['原始'] if (i[-2] != 'true') and ('尸体' not in i[3]) and ('战斗中' not in i[3])]
                                        elif _func['function_name'] == 'setRoom':
                                            self.角色信息['房间'] = _func['parameters'][0]['value']
                                            self.控制台日志('房间', self.角色信息['房间'])
                                        elif _func['function_name'] == 'changeMap':
                                            self.角色信息['地图'] = _func['parameters'][0]['value']
                                            self.控制台日志('地图', self.角色信息['地图'])
                                        elif _func['function_name'] == 'setMaxHP':
                                            self.角色信息['hp_max'] = _func['parameters'][0]['value']
                                        elif _func['function_name'] == 'setMaxSP':
                                            self.角色信息['sp_max'] = _func['parameters'][0]['value']
                                        elif _func['function_name'] == 'setLine':
                                            局_数据 = _func['parameters'][-1]['value']
                                            if 局_数据 == '-':
                                                continue
                                            if _func['parameters'][0]['value'] == 'hpLine_left':
                                                self.角色信息['hp_left'] = int(局_数据)
                                            elif _func['parameters'][0]['value'] == 'mpLine_left':
                                                self.角色信息['sp_left'] = int(局_数据)
                                            elif _func['parameters'][0]['value'] == 'hpLine_right':
                                                pass
                                            elif _func['parameters'][0]['value'] == 'mpLine_right':
                                                pass
                                        elif _func['function_name'] == 'beiDing':
                                            self.控制台日志(f"被顶号:ip{_func['parameters'][0]['value']},延迟{self.全局配置['重连时间']}秒后重登")
                                            time.sleep(self.全局配置['重连时间'])
                                        elif _func['function_name'] == 'setFightTaskImg':
                                            # 无效,这是左上角的图标.不作为释放技能参考,且无法准确判断技能,如十字斩和强打是一个图标
                                            continue
                                        elif _func['function_name'] == 'showLeftTalk':
                                            #TODO 从这开始往下
                                            局_技能名称 = _func['parameters'][0]['value'][:-1]
                                            try:
                                                self.技能配置[局_技能名称]['剩余CD'] = self.技能配置[局_技能名称]['CD'] + 1
                                            except:
                                                pass
                                        elif _func['function_name'] == 'showRightTalk':
                                            self.控制台日志(_func)
                                        elif _func['function_name'] in ['showI', 'showIHide']:
                                            self.背包['背包道具'] = ret_packages(_func)
                                            self.背包['是否使用'] = False
                                        elif _func['function_name'] == '_skillsubs':
                                            self.技能['列表'] = eval(_func['parameters'][0]['value'].replace('false', 'False').replace('true','True'))
                                            self.技能['是否使用'] = False
                                        elif _func['function_name'] == 'setLv':
                                            self.角色信息['等级'] = int(_func['parameters'][0]['value'])
                                        elif _func['function_name'] == 'setExp':
                                            self.角色信息['经验百分比'] = int(round(int(_func['parameters'][0]['value'])/int(_func['parameters'][2]['value']),4) * 100)
                                            if self.全局配置['百分比经验上限'] < 100 and self.角色信息['经验百分比'] >= self.全局配置['百分比经验上限']:
                                                self.控制台日志(f"经验达到上限{self.全局配置['百分比经验上限']}%, 卡住经验")
                                                if self.全局配置['邮箱']:
                                                    通用_发送邮件(self.全局配置['邮箱'], f"账号{self.登录配置['账号']}经验达到上限{self.全局配置['百分比经验上限']}%, 卡住经验")
                                                    self.命令线程锁.acquire()
                                                return
                                        elif _func['function_name'] == '_petinfo':
                                            continue
                                        elif _func['function_name'] == 'showAllotWin':
                                            print(_func)
                                            continue
                                        elif _func['function_name'] == 'addNPCC':
                                            self.消息['NPC对话'].append(_func['parameters'][0]['value'])
                                            self.控制台日志('Npc对话框:', self.消息['NPC对话'][-1])
                                        elif _func['function_name'] == 'showTask':
                                            _t = _func['parameters'][0]['value']
                                            self.任务['列表'] = js_to_python(_t)
                                            self.任务['是否使用'] = False
                                        elif _func['function_name'] == 'showItemDesc':
                                            self.道具说明['内容'] = {
                                                '名称': _func['parameters'][0]['value'],
                                                '描述': _func['parameters'][2]['value']
                                            }
                                            self.控制台日志(self.道具说明['内容'])
                                            self.道具说明['是否使用'] = False
                                        elif _func['function_name'] == 'yDeal':
                                            # 这里是交易
                                            continue
                                        else:
                                            self.控制台日志('未处理格式')
                                            self.控制台日志(_func)
                        except Exception as e:
                            self.在线状态 = False
                            with open('error.txt', 'a', encoding='utf-8') as f:
                                f.write(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S') + '=>' + str(e) + '\n')
                self.在线状态 = False
                await asyncio.sleep(self.全局配置['重连时间'])
                
        def _thread_worker():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(_异步在线())

        self.在线线程 = Thread(target=_thread_worker)
        self.在线线程.start()
        while self.在线状态 is False:
            time.sleep(1)
        self.执行代码('look',0) # 刷新一下房间信息
        
    def 执行代码(self, orders, delay=None):
        with self.命令线程锁:
            orders = orders.split('|')
            self.控制台日志(orders)
            for order in orders:
                if order == '':
                    continue
                try:
                    self.消息['动作'].append(order)
                    if len(self.消息['动作']) > 100:
                        self.消息['动作'].pop(0)
                    res = requests.post(
                        url=self.地址['命令地址'],
                        cookies=self.网络信息['cookies'],
                        data={
                            'action': 'inputCommand',
                            'inputCommand': ('/' + order).encode('gbk'),
                        }
                    )
                except Exception as e:
                    self.控制台日志(e)
                if delay is None:
                    time.sleep(self.全局配置['延时'])
                else:
                    time.sleep(delay)

    def 投递任务(self):
        pass

if __name__ == '__main__':
    myjObjs = {}
    files = os.listdir(os.path.join(os.getcwd(), '账号配置'))
    for index in range(len(files)):
        with open(os.path.join(os.getcwd(), '账号配置', files[index]), 'r', encoding='utf-8') as f:
            configs = json.load(f)
            myjObjs[index] = MYJBase(configs)
            myjObjs[index].保持在线()
        time.sleep(5)
    # configs = {
    #     "本文件名": "配置文件.json",
    #     "区服": "x12",
    #     "账号": "wk93qlyy1",
    #     "密码": "00000000",
    #     "角色序号": "0",
    #     "延时": 0.1,
    #     "非操作延时": 0.1,
    #     "邮箱": "<EMAIL>",
    #     "百分比经验上限": 99,
    #     "日志输出": True,
    #     "在线控制器": True,
    #     "重连时间": 300,
    #     "拖把每日": True,
    #     "在线礼包": True,
    #     "竞技场": True,
    #     "魔化": True,
    #     "如意": True,   
    #     "乐园": True,
    #     "沼泽": True,
    #     "诅咒": True,
    #     "无双": True,
    #     "逆无双": True,
    #     "通天": True,
    #     "罗汉": True
    # }
    # obj = MYJBase(configs)
    # obj.保持在线()
    input()